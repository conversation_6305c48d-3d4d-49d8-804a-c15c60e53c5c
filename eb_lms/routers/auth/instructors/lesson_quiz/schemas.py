# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Schemas for Instructor Lesson Quiz API
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from odoo.addons.eb_api_core.schemas.base import ResponseBase


class LessonQuizListItemSchema(BaseModel):
    """Schema cho item trong danh sách lesson-quiz"""
    id: int = Field(..., description="ID của lesson-quiz relationship")
    quiz_id: int = Field(..., description="ID quiz")
    quiz_name: str = Field(..., description="Tên quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    sequence: int = Field(..., description="Thứ tự trong buổi học")
    is_active: bool = Field(..., description="<PERSON><PERSON> được công bố")
    status: str = Field(..., description="Trạng thái (active/inactive)")
    published_at: Optional[datetime] = Field(None, description="Thời gian công bố")
    published_by_name: Optional[str] = Field(None, description="Người công bố")
    can_publish: bool = Field(..., description="Có thể công bố")
    can_unpublish: bool = Field(..., description="Có thể hủy công bố")
    quiz_question_count: int = Field(..., description="Số câu hỏi")
    quiz_max_score: float = Field(..., description="Điểm tối đa")
    quiz_time_limit: Optional[int] = Field(None, description="Thời gian làm bài (phút)")


class LessonQuizDetailSchema(BaseModel):
    """Schema cho chi tiết lesson-quiz"""
    id: int = Field(..., description="ID của lesson-quiz relationship")
    lesson_id: int = Field(..., description="ID buổi học")
    lesson_name: str = Field(..., description="Tên buổi học")
    quiz_id: int = Field(..., description="ID quiz")
    quiz_name: str = Field(..., description="Tên quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    sequence: int = Field(..., description="Thứ tự trong buổi học")
    is_active: bool = Field(..., description="Đang được công bố")
    status: str = Field(..., description="Trạng thái (active/inactive)")
    published_at: Optional[datetime] = Field(None, description="Thời gian công bố")
    published_by_name: Optional[str] = Field(None, description="Người công bố")
    unpublished_at: Optional[datetime] = Field(None, description="Thời gian hủy công bố")
    unpublished_by_name: Optional[str] = Field(None, description="Người hủy công bố")
    notes: Optional[str] = Field(None, description="Ghi chú")
    can_publish: bool = Field(..., description="Có thể công bố")
    can_unpublish: bool = Field(..., description="Có thể hủy công bố")
    lesson_stage: str = Field(..., description="Trạng thái buổi học")
    lesson_start_datetime: Optional[datetime] = Field(None, description="Thời gian bắt đầu buổi học")
    quiz_question_count: int = Field(..., description="Số câu hỏi")
    quiz_max_score: float = Field(..., description="Điểm tối đa")
    quiz_time_limit: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    quiz_passing_score: float = Field(..., description="Điểm đạt")


class AssignQuizToLessonRequest(BaseModel):
    """Schema cho request gán quiz cho buổi học"""
    quiz_id: int = Field(..., description="ID quiz cần gán")
    sequence: Optional[int] = Field(10, description="Thứ tự trong buổi học")
    notes: Optional[str] = Field(None, description="Ghi chú")


class UpdateLessonQuizRequest(BaseModel):
    """Schema cho request cập nhật lesson-quiz"""
    sequence: Optional[int] = Field(None, description="Thứ tự mới")
    notes: Optional[str] = Field(None, description="Ghi chú mới")


class AvailableQuizSchema(BaseModel):
    """Schema cho quiz có thể gán vào buổi học"""
    id: int = Field(..., description="ID quiz")
    name: str = Field(..., description="Tên quiz")
    code: str = Field(..., description="Mã quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    state: str = Field(..., description="Trạng thái quiz")
    question_count: int = Field(..., description="Số câu hỏi")
    max_score: float = Field(..., description="Điểm tối đa")
    time_limit: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    passing_score: float = Field(..., description="Điểm đạt")
    subject_name: Optional[str] = Field(None, description="Tên môn học")
    class_name: Optional[str] = Field(None, description="Tên lớp học")
    is_currently_published: bool = Field(..., description="Đang được công bố ở buổi học khác")
    published_lesson_count: int = Field(..., description="Số buổi học đang công bố")


class LessonQuizStatsSchema(BaseModel):
    """Schema cho thống kê quiz của buổi học"""
    lesson_id: int = Field(..., description="ID buổi học")
    lesson_name: str = Field(..., description="Tên buổi học")
    total_quizzes: int = Field(..., description="Tổng số quiz")
    active_quizzes: int = Field(..., description="Số quiz đang hoạt động")
    completed_attempts: int = Field(..., description="Số lượt làm bài đã hoàn thành")
    average_score: float = Field(..., description="Điểm trung bình")
    pass_rate: float = Field(..., description="Tỷ lệ đạt (%)")


# Response schemas
class LessonQuizzesResponse(ResponseBase[List[LessonQuizListItemSchema]]):
    """Response cho danh sách quiz của buổi học"""
    meta: Optional[Dict[str, Any]] = Field(None, description="Metadata bổ sung")


class LessonQuizDetailResponse(ResponseBase[LessonQuizDetailSchema]):
    """Response cho chi tiết lesson-quiz"""
    pass


class AvailableQuizzesResponse(ResponseBase[List[AvailableQuizSchema]]):
    """Response cho danh sách quiz có thể gán"""
    meta: Optional[Dict[str, Any]] = Field(None, description="Metadata bổ sung")


class LessonQuizStatsResponse(ResponseBase[LessonQuizStatsSchema]):
    """Response cho thống kê quiz của buổi học"""
    pass


# Student-specific schemas
class StudentLessonQuizSchema(BaseModel):
    """Schema cho quiz trong buổi học từ góc độ học viên"""
    id: int = Field(..., description="ID của lesson-quiz relationship")
    quiz_id: int = Field(..., description="ID quiz")
    quiz_name: str = Field(..., description="Tên quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    quiz_description: Optional[str] = Field(None, description="Mô tả quiz")
    quiz_instruction: Optional[str] = Field(None, description="Hướng dẫn làm bài")
    sequence: int = Field(..., description="Thứ tự trong buổi học")
    is_active: bool = Field(..., description="Đang được công bố")
    published_at: Optional[datetime] = Field(None, description="Thời gian công bố")
    quiz_question_count: int = Field(..., description="Số câu hỏi")
    quiz_max_score: float = Field(..., description="Điểm tối đa")
    quiz_time_limit: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    quiz_passing_score: float = Field(..., description="Điểm đạt")

    # Student-specific info
    student_attempts_count: int = Field(0, description="Số lần đã làm")
    student_best_score: Optional[float] = Field(None, description="Điểm cao nhất")
    student_last_attempt_at: Optional[datetime] = Field(None, description="Lần làm cuối")
    can_attempt: bool = Field(..., description="Có thể làm bài")


class StudentLessonQuizzesResponse(ResponseBase[List[StudentLessonQuizSchema]]):
    """Response cho danh sách quiz của buổi học từ góc độ học viên"""
    meta: Optional[Dict[str, Any]] = Field(None, description="Metadata bổ sung")


class QuizPublishHistorySchema(BaseModel):
    """Schema cho lịch sử publish/unpublish quiz"""
    action: str = Field(..., description="Hành động: publish/unpublish")
    timestamp: datetime = Field(..., description="Thời gian thực hiện")
    user_name: str = Field(..., description="Người thực hiện")
    lesson_name: str = Field(..., description="Tên buổi học")
    notes: Optional[str] = Field(None, description="Ghi chú")


class QuizPublishHistoryResponse(ResponseBase[List[QuizPublishHistorySchema]]):
    """Response cho lịch sử publish quiz"""
    pass
