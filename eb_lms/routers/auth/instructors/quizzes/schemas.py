# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class InstructorQuizListItemSchema(BaseModel):
    """Schema cho quiz trong danh sách của giảng viên"""
    id: int = Field(..., description="ID quiz")
    name: str = Field(..., description="Tên quiz")
    code: str = Field(..., description="Mã quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    subject_name: Optional[str] = Field(None, description="Tên môn học")
    class_name: Optional[str] = Field(None, description="<PERSON>ên lớp học")
    
    max_score: float = Field(..., description="Điểm tối đa")
    passing_score: float = Field(..., description="Điểm đạt")
    question_count: int = Field(0, description="Số lượng câu hỏi")
    
    start_date: Optional[datetime] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[datetime] = Field(None, description="Ngày kết thúc")
    state: str = Field(..., description="Trạng thái")
    
    # Thống kê
    student_count: int = Field(0, description="Số học viên đã làm")
    attempt_count: int = Field(0, description="Tổng số lần làm bài")
    average_score: float = Field(0.0, description="Điểm trung bình")
    pass_rate: float = Field(0.0, description="Tỷ lệ đạt")
    
    # Chấm điểm
    pending_grading_count: int = Field(0, description="Số bài cần chấm")


class StudentAttemptDetailSchema(BaseModel):
    """Schema cho chi tiết attempt của học viên"""
    id: int = Field(..., description="ID attempt")
    student_id: int = Field(..., description="ID học viên")
    student_name: str = Field(..., description="Tên học viên")
    student_code: str = Field(..., description="Mã học viên")
    
    attempt_number: int = Field(..., description="Số lần thử")
    start_time: datetime = Field(..., description="Thời gian bắt đầu")
    end_time: Optional[datetime] = Field(None, description="Thời gian kết thúc")
    time_spent: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    
    score: Optional[float] = Field(None, description="Điểm số")
    max_score: float = Field(..., description="Điểm tối đa")
    percentage: Optional[float] = Field(None, description="Phần trăm")
    is_passed: Optional[bool] = Field(None, description="Đã đạt")
    
    state: str = Field(..., description="Trạng thái")
    needs_grading: bool = Field(False, description="Cần chấm điểm thủ công")
    feedback: Optional[str] = Field(None, description="Nhận xét")


class QuizAttemptAnswerDetailSchema(BaseModel):
    """Schema cho chi tiết câu trả lời"""
    id: int = Field(..., description="ID answer")
    question_id: int = Field(..., description="ID câu hỏi")
    question_name: str = Field(..., description="Nội dung câu hỏi")
    question_type: str = Field(..., description="Loại câu hỏi")
    question_score: float = Field(..., description="Điểm câu hỏi")
    
    # Đáp án của học viên
    answer_id: Optional[int] = Field(None, description="ID đáp án đã chọn")
    answer_text: Optional[str] = Field(None, description="Text đáp án đã chọn")
    text_answer: Optional[str] = Field(None, description="Câu trả lời tự luận")
    
    # Đáp án đúng
    correct_answer_id: Optional[int] = Field(None, description="ID đáp án đúng")
    correct_answer_text: Optional[str] = Field(None, description="Text đáp án đúng")
    
    # Chấm điểm
    score: Optional[float] = Field(None, description="Điểm đạt được")
    is_correct: Optional[bool] = Field(None, description="Trả lời đúng")
    needs_grading: bool = Field(False, description="Cần chấm thủ công")
    instructor_feedback: Optional[str] = Field(None, description="Nhận xét của giảng viên")
    
    answered_at: Optional[datetime] = Field(None, description="Thời gian trả lời")


class GradeAttemptRequest(BaseModel):
    """Schema cho request chấm điểm attempt"""
    attempt_id: int = Field(..., description="ID attempt")
    answers: List[Dict[str, Any]] = Field(..., description="Danh sách điểm cho từng câu")
    feedback: Optional[str] = Field(None, description="Nhận xét chung")


class GradeAnswerRequest(BaseModel):
    """Schema cho request chấm điểm từng câu"""
    score: float = Field(..., ge=0, description="Điểm số")
    is_correct: bool = Field(..., description="Đúng hay sai")
    feedback: Optional[str] = Field(None, description="Nhận xét")


class QuizAnalyticsSchema(BaseModel):
    """Schema cho analytics quiz"""
    quiz_id: int = Field(..., description="ID quiz")
    quiz_name: str = Field(..., description="Tên quiz")
    
    # Thống kê tổng quan
    total_students: int = Field(0, description="Tổng số học viên")
    completed_students: int = Field(0, description="Số học viên đã hoàn thành")
    completion_rate: float = Field(0.0, description="Tỷ lệ hoàn thành")
    
    total_attempts: int = Field(0, description="Tổng số lần làm bài")
    average_attempts: float = Field(0.0, description="Trung bình lần làm bài/học viên")
    
    # Thống kê điểm số
    average_score: float = Field(0.0, description="Điểm trung bình")
    highest_score: float = Field(0.0, description="Điểm cao nhất")
    lowest_score: float = Field(0.0, description="Điểm thấp nhất")
    pass_rate: float = Field(0.0, description="Tỷ lệ đạt")
    
    # Thống kê thời gian
    average_time: float = Field(0.0, description="Thời gian trung bình (phút)")
    fastest_time: float = Field(0.0, description="Thời gian nhanh nhất (phút)")
    slowest_time: float = Field(0.0, description="Thời gian chậm nhất (phút)")
    
    # Phân bố điểm
    score_distribution: Dict[str, int] = Field(default_factory=dict, description="Phân bố điểm theo khoảng")
    
    # Câu hỏi khó nhất/dễ nhất
    hardest_questions: List[Dict[str, Any]] = Field(default_factory=list, description="Câu hỏi khó nhất")
    easiest_questions: List[Dict[str, Any]] = Field(default_factory=list, description="Câu hỏi dễ nhất")


class InstructorQuizzesResponse(BaseModel):
    """Schema cho response danh sách quiz của giảng viên"""
    quizzes: List[InstructorQuizListItemSchema] = Field(..., description="Danh sách quiz")
    total_count: int = Field(..., description="Tổng số quiz")
    page: int = Field(..., description="Trang hiện tại")
    limit: int = Field(..., description="Số item mỗi trang")


class QuizAttemptsResponse(BaseModel):
    """Schema cho response danh sách attempt của quiz"""
    attempts: List[StudentAttemptDetailSchema] = Field(..., description="Danh sách attempt")
    total_count: int = Field(..., description="Tổng số attempt")
    quiz_info: Dict[str, Any] = Field(..., description="Thông tin quiz")


class AttemptDetailResponse(BaseModel):
    """Schema cho response chi tiết attempt"""
    attempt: StudentAttemptDetailSchema = Field(..., description="Thông tin attempt")
    answers: List[QuizAttemptAnswerDetailSchema] = Field(..., description="Danh sách câu trả lời")
    quiz_info: Dict[str, Any] = Field(..., description="Thông tin quiz")


class PendingGradingResponse(BaseModel):
    """Schema cho response danh sách bài cần chấm"""
    attempts: List[StudentAttemptDetailSchema] = Field(..., description="Danh sách attempt cần chấm")
    total_count: int = Field(..., description="Tổng số attempt cần chấm")
    by_quiz: Dict[str, int] = Field(..., description="Số lượng theo từng quiz")


# Quiz Creation Schemas
class QuizAnswerCreateSchema(BaseModel):
    """Schema cho tạo đáp án"""
    text: str = Field(..., description="Nội dung đáp án")
    is_correct: bool = Field(False, description="Đáp án đúng")
    explanation: Optional[str] = Field(None, description="Giải thích")


class QuizQuestionCreateSchema(BaseModel):
    """Schema cho tạo câu hỏi"""
    text: str = Field(..., description="Nội dung câu hỏi")
    question_type: str = Field(..., description="Loại câu hỏi: single_choice, multiple_choice, true_false, essay, short_answer, matching")
    score: float = Field(..., ge=0, description="Điểm số")
    explanation: Optional[str] = Field(None, description="Giải thích")
    answers: List[QuizAnswerCreateSchema] = Field(default_factory=list, description="Danh sách đáp án")


class CreateQuizRequest(BaseModel):
    """Schema cho request tạo quiz"""
    name: str = Field(..., description="Tên quiz")
    description: Optional[str] = Field(None, description="Mô tả")
    instruction: Optional[str] = Field(None, description="Hướng dẫn làm bài")
    quiz_type: str = Field("quiz", description="Loại quiz: quiz, assignment, midterm, final, project")

    subject_id: Optional[int] = Field(None, description="ID môn học")
    class_id: Optional[int] = Field(None, description="ID lớp học")

    max_score: float = Field(100.0, ge=0, description="Điểm tối đa")
    passing_score: float = Field(50.0, ge=0, description="Điểm đạt")
    time_limit: Optional[int] = Field(None, ge=1, description="Thời gian làm bài (phút)")

    start_date: Optional[datetime] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[datetime] = Field(None, description="Ngày kết thúc")

    is_randomized: bool = Field(False, description="Ngẫu nhiên thứ tự câu hỏi")
    show_correct_answers: bool = Field(False, description="Hiển thị đáp án đúng")
    show_result_immediately: bool = Field(True, description="Hiển thị kết quả ngay")

    questions: List[QuizQuestionCreateSchema] = Field(default_factory=list, description="Danh sách câu hỏi")


class UpdateQuizRequest(BaseModel):
    """Schema cho request cập nhật quiz"""
    name: Optional[str] = Field(None, description="Tên quiz")
    description: Optional[str] = Field(None, description="Mô tả")
    instruction: Optional[str] = Field(None, description="Hướng dẫn làm bài")
    quiz_type: Optional[str] = Field(None, description="Loại quiz")

    subject_id: Optional[int] = Field(None, description="ID môn học")
    class_id: Optional[int] = Field(None, description="ID lớp học")

    max_score: Optional[float] = Field(None, ge=0, description="Điểm tối đa")
    passing_score: Optional[float] = Field(None, ge=0, description="Điểm đạt")
    time_limit: Optional[int] = Field(None, ge=1, description="Thời gian làm bài (phút)")

    start_date: Optional[datetime] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[datetime] = Field(None, description="Ngày kết thúc")

    is_randomized: Optional[bool] = Field(None, description="Ngẫu nhiên thứ tự câu hỏi")
    show_correct_answers: Optional[bool] = Field(None, description="Hiển thị đáp án đúng")
    show_result_immediately: Optional[bool] = Field(None, description="Hiển thị kết quả ngay")


class AddQuestionsRequest(BaseModel):
    """Schema cho request thêm câu hỏi vào quiz"""
    questions: List[QuizQuestionCreateSchema] = Field(..., description="Danh sách câu hỏi cần thêm")


class QuizCreatedResponse(BaseModel):
    """Schema cho response quiz đã tạo"""
    id: int = Field(..., description="ID quiz")
    name: str = Field(..., description="Tên quiz")
    code: str = Field(..., description="Mã quiz")
    state: str = Field(..., description="Trạng thái")
    question_count: int = Field(0, description="Số lượng câu hỏi")
    max_score: float = Field(..., description="Điểm tối đa")
    created_at: datetime = Field(..., description="Thời gian tạo")


class QuizDetailResponse(BaseModel):
    """Schema cho chi tiết quiz của giảng viên"""
    id: int = Field(..., description="ID quiz")
    name: str = Field(..., description="Tên quiz")
    code: str = Field(..., description="Mã quiz")
    description: str = Field("", description="Mô tả quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    subject_name: Optional[str] = Field(None, description="Tên môn học")
    class_name: Optional[str] = Field(None, description="Tên lớp học")

    # Cấu hình quiz
    max_score: float = Field(..., description="Điểm tối đa")
    passing_score: float = Field(..., description="Điểm đạt")
    time_limit: Optional[int] = Field(None, description="Thời gian giới hạn (phút)")
    show_correct_answers: bool = Field(False, description="Hiển thị đáp án đúng")
    is_randomized: bool = Field(False, description="Ngẫu nhiên thứ tự câu hỏi")

    # Thời gian
    start_date: Optional[str] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[str] = Field(None, description="Ngày kết thúc")

    # Trạng thái
    state: str = Field(..., description="Trạng thái")
    is_published: bool = Field(False, description="Đã công bố")

    # Thống kê
    question_count: int = Field(0, description="Số lượng câu hỏi")
    attempt_count: int = Field(0, description="Tổng số lần làm bài")
    student_count: int = Field(0, description="Số học viên đã làm")
    average_score: float = Field(0.0, description="Điểm trung bình")
    pass_rate: float = Field(0.0, description="Tỷ lệ đạt")
    pending_grading_count: int = Field(0, description="Số bài cần chấm")
