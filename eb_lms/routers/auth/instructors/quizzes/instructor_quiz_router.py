# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Instructor Quiz API Router

Endpoints for instructor quiz management:
- GET /quizzes: Get instructor's quizzes
- POST /quizzes: Create new quiz
- PUT /quizzes/{quiz_id}: Update quiz
- DELETE /quizzes/{quiz_id}: Delete quiz
- POST /quizzes/{quiz_id}/ready: Mark quiz as ready for assignment
- POST /quizzes/{quiz_id}/questions: Add questions to quiz
- GET /quizzes/{quiz_id}/attempts: Get all attempts for a quiz
- GET /quizzes/{quiz_id}/analytics: Get quiz analytics
- GET /attempts/{attempt_id}: Get attempt details for grading
- POST /attempts/{attempt_id}/grade: Grade an attempt
- GET /grading/pending: Get pending grading items
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from odoo import _
from odoo.exceptions import ValidationError, AccessError

from odoo.addons.eb_api_core.dependencies.auth import get_current_user
from odoo.addons.eb_lms.utils.helpers import safe_value
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_lms.dependencies.role_dependencies import require_instructor
from odoo.addons.eb_lms.utils.helpers import safe_value

from .schemas import (
    InstructorQuizListItemSchema,
    StudentAttemptDetailSchema,
    QuizAttemptAnswerDetailSchema,
    QuizAnalyticsSchema,
    GradeAttemptRequest,
    GradeAnswerRequest,
    InstructorQuizzesResponse,
    QuizAttemptsResponse,
    AttemptDetailResponse,
    PendingGradingResponse,
    CreateQuizRequest,
    UpdateQuizRequest,
    AddQuestionsRequest,
    QuizCreatedResponse,
    QuizDetailResponse,
)
from ..lesson_quiz.schemas import AvailableQuizSchema, AvailableQuizzesResponse

_logger = logging.getLogger(__name__)

router = APIRouter(prefix="/quizzes", tags=["Instructor Quizzes"])


@router.get(
    "",
    response_model=ResponseBase[InstructorQuizzesResponse],
    summary="Lấy danh sách quiz của giảng viên",
    description="Lấy danh sách tất cả quiz mà giảng viên được phân công"
)
def get_instructor_quizzes(
    page: int = Query(1, ge=1, description="Số trang"),
    limit: int = Query(20, ge=1, le=100, description="Số item mỗi trang"),
    quiz_type: Optional[str] = Query(None, description="Lọc theo loại quiz"),
    subject_id: Optional[int] = Query(None, description="Lọc theo môn học"),
    class_id: Optional[int] = Query(None, description="Lọc theo lớp học"),
    state: Optional[str] = Query(None, description="Lọc theo trạng thái"),
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy danh sách quiz của giảng viên"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )
        
        # Build domain cho quiz search
        domain = [
            ("instructor_id", "=", instructor.id),  # Quiz của giảng viên này
        ]
        
        # Thêm filters
        if quiz_type:
            domain.append(("quiz_type", "=", quiz_type))
        if subject_id:
            domain.append(("subject_id", "=", subject_id))
        if class_id:
            domain.append(("class_id", "=", class_id))
        if state:
            domain.append(("state", "=", state))
        
        # Tính offset
        offset = (page - 1) * limit
        
        # Lấy quizzes
        quizzes = current_user.env["eb.quiz"].search(
            domain, 
            limit=limit, 
            offset=offset,
            order="create_date desc"
        )
        
        total_count = current_user.env["eb.quiz"].search_count(domain)
        
        # Convert to schema
        quiz_items = []
        for quiz in quizzes:
            # Đếm số bài cần chấm thủ công (state = completed nghĩa là đã hoàn thành nhưng chưa chấm điểm)
            pending_grading = current_user.env["eb.quiz.attempt"].search_count([
                ("quiz_id", "=", quiz.id),
                ("state", "=", "completed")
            ])
            
            quiz_item = InstructorQuizListItemSchema(
                id=quiz.id,
                name=quiz.name,
                code=quiz.code,
                quiz_type=quiz.quiz_type,
                subject_name=quiz.subject_id.name if quiz.subject_id else None,
                class_name=quiz.class_id.name if quiz.class_id else None,
                max_score=quiz.max_score,
                passing_score=quiz.passing_score,
                question_count=quiz.question_count or 0,
                start_date=safe_value(quiz.start_date),
                end_date=safe_value(quiz.end_date),
                state=quiz.state,
                student_count=quiz.student_count or 0,
                attempt_count=quiz.attempt_count or 0,
                average_score=quiz.average_score or 0.0,
                pass_rate=quiz.pass_rate or 0.0,
                pending_grading_count=pending_grading,
            )
            quiz_items.append(quiz_item)
        
        return ResponseBase(
            success=True,
            message=f"Lấy danh sách quiz thành công. Tìm thấy {total_count} quiz.",
            data=InstructorQuizzesResponse(
                quizzes=quiz_items,
                total_count=total_count,
                page=page,
                limit=limit
            )
        )

    except Exception as e:
        _logger.error(f"Lỗi khi lấy danh sách quiz giảng viên: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "",
    response_model=ResponseBase[QuizCreatedResponse],
    summary="Tạo quiz mới",
    description="Tạo quiz mới cho giảng viên"
)
def create_quiz(
    request: CreateQuizRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Tạo quiz mới"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Validate subject và class nếu có
        if request.subject_id:
            subject = current_user.env["eb.subject.subject"].browse(request.subject_id)
            if not subject.exists():
                raise HTTPException(
                    status_code=400,
                    detail="Môn học không tồn tại"
                )

        if request.class_id:
            class_obj = current_user.env["eb.class.class"].browse(request.class_id)
            if not class_obj.exists():
                raise HTTPException(
                    status_code=400,
                    detail="Lớp học không tồn tại"
                )

            # Kiểm tra instructor có quyền với class này không
            if (class_obj.primary_instructor_id.id != instructor.id and
                instructor.id not in class_obj.assistant_instructor_ids.ids):
                raise HTTPException(
                    status_code=403,
                    detail="Bạn không có quyền tạo quiz cho lớp học này"
                )

        # Tạo quiz
        quiz_vals = {
            "name": request.name,
            "description": request.description,
            "instruction": request.instruction,
            "quiz_type": request.quiz_type,
            "subject_id": request.subject_id,
            "class_id": request.class_id,
            "instructor_id": instructor.id,
            "max_score": request.max_score,
            "passing_score": request.passing_score,
            "time_limit": safe_value(request.time_limit),
            "start_date": safe_value(request.start_date),
            "end_date": safe_value(request.end_date),
            "is_randomized": request.is_randomized,
            "show_correct_answers": request.show_correct_answers,
            "show_result_immediately": request.show_result_immediately,
            "state": "draft",
            "is_published": False,
        }

        quiz = current_user.env["eb.quiz"].create(quiz_vals)

        # Tạo câu hỏi nếu có
        if request.questions:
            for question_data in request.questions:
                question_vals = {
                    "quiz_id": quiz.id,
                    "name": question_data.text,
                    "question_type": question_data.question_type,
                    "score": question_data.score,
                    "explanation": question_data.explanation,
                }

                question = current_user.env["eb.quiz.question"].create(question_vals)

                # Tạo đáp án
                for answer_data in question_data.answers:
                    answer_vals = {
                        "question_id": question.id,
                        "name": answer_data.text,
                        "is_correct": answer_data.is_correct,
                        "explanation": answer_data.explanation,
                    }
                    current_user.env["eb.quiz.answer"].create(answer_vals)

        # Tính lại max_score dựa trên tổng điểm câu hỏi
        if request.questions:
            total_score = sum(q.score for q in request.questions)
            quiz.write({"max_score": total_score})

        return ResponseBase(
            success=True,
            message="Tạo quiz thành công",
            data=QuizCreatedResponse(
                id=quiz.id,
                name=quiz.name,
                code=quiz.code,
                state=quiz.state,
                question_count=quiz.question_count,
                max_score=quiz.max_score,
                created_at=safe_value(quiz.create_date),
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi tạo quiz: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/{quiz_id}",
    response_model=ResponseBase[QuizDetailResponse],
    summary="Lấy chi tiết quiz",
    description="Lấy chi tiết quiz bao gồm câu hỏi và thống kê"
)
def get_quiz_detail(
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy chi tiết quiz"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập quiz này"
            )

        # Build quiz detail response
        quiz_detail = QuizDetailResponse(
            id=quiz.id,
            name=quiz.name,
            code=quiz.code,
            description=quiz.description or "",
            quiz_type=quiz.quiz_type,
            subject_name=quiz.subject_id.name if quiz.subject_id else None,
            class_name=quiz.class_id.name if quiz.class_id else None,
            max_score=quiz.max_score,
            passing_score=quiz.passing_score,
            time_limit=safe_value(quiz.time_limit),
            show_correct_answers=quiz.show_correct_answers,
            is_randomized=quiz.is_randomized,
            start_date=safe_value(quiz.start_date).isoformat() if safe_value(quiz.start_date) else None if quiz.start_date else None,
            end_date=safe_value(quiz.end_date).isoformat() if safe_value(quiz.end_date) else None if quiz.end_date else None,
            state=quiz.state,
            is_currently_published=quiz.is_currently_published,
            published_lesson_count=quiz.published_lesson_count,
            question_count=quiz.question_count,
            attempt_count=quiz.attempt_count,
            student_count=quiz.student_count,
            average_score=quiz.average_score,
            pass_rate=quiz.pass_rate,
            pending_grading_count=len(quiz.attempt_ids.filtered(lambda a: a.state == 'completed'))
        )

        return ResponseBase(
            success=True,
            message="Lấy chi tiết quiz thành công",
            data=quiz_detail
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy chi tiết quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.put(
    "/{quiz_id}",
    response_model=ResponseBase[QuizCreatedResponse],
    summary="Cập nhật quiz",
    description="Cập nhật thông tin quiz"
)
def update_quiz(
    quiz_id: int,
    request: UpdateQuizRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Cập nhật quiz"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền chỉnh sửa quiz này"
            )

        # Kiểm tra trạng thái - không cho sửa quiz đang được publish và có attempt
        if quiz.is_currently_published and quiz.attempt_count > 0:
            raise HTTPException(
                status_code=400,
                detail="Không thể sửa quiz đã có học viên làm bài"
            )

        # Chuẩn bị dữ liệu update
        update_vals = {}

        if request.name is not None:
            update_vals["name"] = request.name
        if request.description is not None:
            update_vals["description"] = request.description
        if request.instruction is not None:
            update_vals["instruction"] = request.instruction
        if request.quiz_type is not None:
            update_vals["quiz_type"] = request.quiz_type
        if request.subject_id is not None:
            update_vals["subject_id"] = request.subject_id
        if request.class_id is not None:
            update_vals["class_id"] = request.class_id
        if request.max_score is not None:
            update_vals["max_score"] = request.max_score
        if request.passing_score is not None:
            update_vals["passing_score"] = request.passing_score
        if request.time_limit is not None:
            update_vals["time_limit"] = request.time_limit
        if request.start_date is not None:
            update_vals["start_date"] = request.start_date
        if request.end_date is not None:
            update_vals["end_date"] = request.end_date
        if request.is_randomized is not None:
            update_vals["is_randomized"] = request.is_randomized
        if request.show_correct_answers is not None:
            update_vals["show_correct_answers"] = request.show_correct_answers
        if request.show_result_immediately is not None:
            update_vals["show_result_immediately"] = request.show_result_immediately

        # Cập nhật quiz
        if update_vals:
            quiz.write(update_vals)

        return ResponseBase(
            success=True,
            message="Cập nhật quiz thành công",
            data=QuizCreatedResponse(
                id=quiz.id,
                name=quiz.name,
                code=quiz.code,
                state=quiz.state,
                question_count=quiz.question_count,
                max_score=quiz.max_score,
                created_at=safe_value(quiz.create_date),
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi cập nhật quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.delete(
    "/{quiz_id}",
    response_model=ResponseBase[Dict[str, Any]],
    summary="Xóa quiz",
    description="Xóa quiz (chỉ được xóa quiz chưa có attempt)"
)
def delete_quiz(
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Xóa quiz"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền xóa quiz này"
            )

        # Kiểm tra có attempt không
        if quiz.attempt_count > 0:
            raise HTTPException(
                status_code=400,
                detail="Không thể xóa quiz đã có học viên làm bài"
            )

        quiz_name = quiz.name
        quiz.unlink()

        return ResponseBase(
            success=True,
            message=f"Xóa quiz '{quiz_name}' thành công",
            data={"deleted_quiz_id": quiz_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi xóa quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "/{quiz_id}/ready",
    response_model=ResponseBase[Dict[str, Any]],
    summary="Đánh dấu quiz sẵn sàng",
    description="Đánh dấu quiz sẵn sàng để gán vào buổi học"
)
def mark_quiz_ready(
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Đánh dấu quiz sẵn sàng để gán vào buổi học"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền công bố quiz này"
            )

        # Kiểm tra quiz có câu hỏi không
        if quiz.question_count == 0:
            raise HTTPException(
                status_code=400,
                detail="Không thể công bố quiz không có câu hỏi"
            )

        # Mark quiz as ready (deprecated publish method)
        quiz.action_publish()  # This now just sets state = 'ready'

        return ResponseBase(
            success=True,
            message=f"Quiz '{quiz.name}' đã sẵn sàng để gán vào buổi học",
            data={
                "quiz_id": quiz.id,
                "quiz_name": quiz.name,
                "state": quiz.state,
                "is_currently_published": quiz.is_currently_published,
                "published_lesson_count": quiz.published_lesson_count,
                "note": "Để công bố cho học viên, vui lòng gán quiz vào buổi học và publish từ lesson management."
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi công bố quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "/{quiz_id}/questions",
    response_model=ResponseBase[Dict[str, Any]],
    summary="Thêm câu hỏi vào quiz",
    description="Thêm danh sách câu hỏi vào quiz"
)
def add_questions_to_quiz(
    quiz_id: int,
    request: AddQuestionsRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Thêm câu hỏi vào quiz"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền chỉnh sửa quiz này"
            )

        # Kiểm tra trạng thái
        if quiz.is_currently_published and quiz.attempt_count > 0:
            raise HTTPException(
                status_code=400,
                detail="Không thể thêm câu hỏi vào quiz đã có học viên làm bài"
            )

        # Thêm câu hỏi
        questions_created = 0
        for question_data in request.questions:
            question_vals = {
                "quiz_id": quiz.id,
                "name": question_data.text,
                "question_type": question_data.question_type,
                "score": question_data.score,
                "explanation": question_data.explanation,
            }

            question = current_user.env["eb.quiz.question"].create(question_vals)
            questions_created += 1

            # Tạo đáp án
            for answer_data in question_data.answers:
                answer_vals = {
                    "question_id": question.id,
                    "name": answer_data.text,
                    "is_correct": answer_data.is_correct,
                    "explanation": answer_data.explanation,
                }
                current_user.env["eb.quiz.answer"].create(answer_vals)

        # Cập nhật max_score
        total_score = sum(q.score for q in quiz.question_ids)
        quiz.write({"max_score": total_score})

        return ResponseBase(
            success=True,
            message=f"Thêm {questions_created} câu hỏi vào quiz thành công",
            data={
                "quiz_id": quiz.id,
                "questions_added": questions_created,
                "total_questions": quiz.question_count,
                "new_max_score": quiz.max_score,
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi thêm câu hỏi vào quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/{quiz_id}/analytics",
    response_model=ResponseBase[QuizAnalyticsSchema],
    summary="Lấy thống kê analytics của quiz",
    description="Lấy thống kê chi tiết về quiz bao gồm điểm số, thời gian, phân bố"
)
def get_quiz_analytics(
    quiz_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy thống kê analytics của quiz"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )

        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập quiz này"
            )

        # Lấy tất cả attempts của quiz
        attempts = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id", "=", quiz_id),
            ("state", "in", ["completed", "graded"])
        ])

        # Tính toán thống kê cơ bản
        total_attempts = len(attempts)
        completed_students = len(set(attempts.mapped("student_id.id")))

        # Thống kê điểm số
        scores = [attempt.score for attempt in attempts if attempt.score is not False]
        average_score = sum(scores) / len(scores) if scores else 0.0
        highest_score = max(scores) if scores else 0.0
        lowest_score = min(scores) if scores else 0.0

        # Tỷ lệ đạt
        passed_attempts = len([s for s in scores if s >= quiz.passing_score])
        pass_rate = (passed_attempts / len(scores) * 100) if scores else 0.0

        # Thống kê thời gian
        times = [attempt.time_spent for attempt in attempts if attempt.time_spent]
        average_time = sum(times) / len(times) if times else 0.0
        fastest_time = min(times) if times else 0.0
        slowest_time = max(times) if times else 0.0

        # Phân bố điểm theo khoảng
        score_distribution = {
            "0-2": 0, "2-4": 0, "4-6": 0, "6-8": 0, "8-10": 0
        }

        for score in scores:
            if score < 2:
                score_distribution["0-2"] += 1
            elif score < 4:
                score_distribution["2-4"] += 1
            elif score < 6:
                score_distribution["4-6"] += 1
            elif score < 8:
                score_distribution["6-8"] += 1
            else:
                score_distribution["8-10"] += 1

        # Tạo analytics response
        analytics = QuizAnalyticsSchema(
            quiz_id=quiz.id,
            quiz_name=quiz.name,
            total_students=quiz.student_count or 0,
            completed_students=completed_students,
            completion_rate=(completed_students / quiz.student_count * 100) if quiz.student_count else 0.0,
            total_attempts=total_attempts,
            average_attempts=(total_attempts / completed_students) if completed_students else 0.0,
            average_score=average_score,
            highest_score=highest_score,
            lowest_score=lowest_score,
            pass_rate=pass_rate,
            average_time=average_time,
            fastest_time=fastest_time,
            slowest_time=slowest_time,
            score_distribution=score_distribution,
        )

        return ResponseBase(
            success=True,
            message="Lấy thống kê quiz thành công",
            data=analytics
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy thống kê quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/{quiz_id}/attempts",
    response_model=ResponseBase[QuizAttemptsResponse],
    summary="Lấy danh sách attempt của quiz",
    description="Lấy danh sách tất cả attempt của học viên cho quiz này"
)
def get_quiz_attempts(
    quiz_id: int,
    page: int = Query(1, ge=1, description="Số trang"),
    limit: int = Query(50, ge=1, le=200, description="Số item mỗi trang"),
    student_name: Optional[str] = Query(None, description="Tìm theo tên học viên"),
    state: Optional[str] = Query(None, description="Lọc theo trạng thái"),
    needs_grading: Optional[bool] = Query(None, description="Lọc bài cần chấm"),
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy danh sách attempt của quiz"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)
        
        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )
        
        # Lấy quiz và kiểm tra quyền
        quiz = current_user.env["eb.quiz"].browse(quiz_id)
        if not quiz.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy quiz"
            )
        
        if quiz.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập quiz này"
            )
        
        # Build domain cho attempt search
        domain = [("quiz_id", "=", quiz.id)]
        
        # Thêm filters
        if state:
            domain.append(("state", "=", state))
        if needs_grading is not None:
            # needs_grading=True nghĩa là state="completed", needs_grading=False nghĩa là state="graded"
            if needs_grading:
                domain.append(("state", "=", "completed"))
            else:
                domain.append(("state", "=", "graded"))
        if student_name:
            domain.append(("student_id.name", "ilike", student_name))
        
        # Tính offset
        offset = (page - 1) * limit
        
        # Lấy attempts
        attempts = current_user.env["eb.quiz.attempt"].search(
            domain, 
            limit=limit, 
            offset=offset,
            order="start_time desc"
        )
        
        total_count = current_user.env["eb.quiz.attempt"].search_count(domain)
        
        # Convert to schema
        attempt_items = []
        for attempt in attempts:
            attempt_schema = StudentAttemptDetailSchema(
                id=attempt.id,
                student_id=attempt.student_id.id,
                student_name=attempt.student_id.name,
                student_code=attempt.student_id.code,
                attempt_number=attempt.attempt_number,
                start_time=safe_value(attempt.start_time),
                end_time=safe_value(attempt.end_time),
                time_spent=safe_value(attempt.time_spent),
                score=attempt.score,
                max_score=attempt.max_score,
                percentage=attempt.percentage,
                is_passed=attempt.is_passed,
                state=attempt.state,
                needs_grading=(attempt.state == "completed"),  # completed = cần chấm điểm
                feedback=safe_value(attempt.feedback),
            )
            attempt_items.append(attempt_schema)
        
        quiz_info = {
            "id": quiz.id,
            "name": quiz.name,
            "code": quiz.code,
            "max_score": quiz.max_score,
            "passing_score": quiz.passing_score,
        }
        
        return ResponseBase(
            success=True,
            message=f"Lấy danh sách attempt thành công. Tìm thấy {total_count} attempt.",
            data=QuizAttemptsResponse(
                attempts=attempt_items,
                total_count=total_count,
                quiz_info=quiz_info
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy danh sách attempt quiz {quiz_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/attempts/{attempt_id}",
    response_model=ResponseBase[AttemptDetailResponse],
    summary="Lấy chi tiết attempt để chấm điểm",
    description="Lấy chi tiết attempt bao gồm tất cả câu trả lời để chấm điểm"
)
def get_attempt_detail(
    attempt_id: int,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy chi tiết attempt để chấm điểm"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy attempt và kiểm tra quyền
        attempt = current_user.env["eb.quiz.attempt"].browse(attempt_id)
        if not attempt.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy attempt"
            )

        if attempt.quiz_id.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền truy cập attempt này"
            )

        # Lấy chi tiết attempt
        attempt_detail = StudentAttemptDetailSchema(
            id=attempt.id,
            student_id=attempt.student_id.id,
            student_name=attempt.student_id.name,
            student_code=attempt.student_id.code,
            attempt_number=attempt.attempt_number,
            start_time=safe_value(attempt.start_time),
            end_time=safe_value(attempt.end_time),
            time_spent=safe_value(attempt.time_spent),
            score=attempt.score,
            max_score=attempt.max_score,
            percentage=attempt.percentage,
            is_passed=attempt.is_passed,
            state=attempt.state,
            needs_grading=(attempt.state == "completed"),  # completed = cần chấm điểm
            feedback=safe_value(attempt.feedback),
        )

        # Lấy danh sách câu trả lời
        answers = []
        attempt_answers = current_user.env["eb.quiz.attempt.answer"].search([
            ("attempt_id", "=", attempt.id)
        ])

        # Sort manually by question sequence
        attempt_answers = attempt_answers.sorted(lambda x: x.question_id.sequence)

        for answer in attempt_answers:
            question = answer.question_id

            # Lấy đáp án đúng cho câu hỏi trắc nghiệm
            correct_answer_id = None
            correct_answer_text = None
            if question.question_type in ['single_choice', 'true_false']:
                correct_answer = question.answer_ids.filtered('is_correct')
                if correct_answer:
                    correct_answer_id = correct_answer[0].id
                    correct_answer_text = correct_answer[0].name

            # Lấy text đáp án đã chọn
            answer_text = None
            if answer.answer_id:
                answer_text = answer.answer_id.name

            # Handle Odoo False values
            text_answer = answer.text_answer if answer.text_answer is not False else None
            instructor_feedback = answer.feedback if answer.feedback is not False else None

            answer_detail = QuizAttemptAnswerDetailSchema(
                id=answer.id,
                question_id=question.id,
                question_name=question.name,
                question_type=question.question_type,
                question_score=question.score,
                answer_id=answer.answer_id.id if answer.answer_id else None,
                answer_text=answer_text,
                text_answer=text_answer,
                correct_answer_id=correct_answer_id,
                correct_answer_text=correct_answer_text,
                score=answer.score,
                is_correct=answer.is_correct,
                needs_grading=(answer.state == "pending"),  # pending = cần chấm điểm
                instructor_feedback=instructor_feedback,
                answered_at=safe_value(attempt.start_time),  # Sử dụng thời gian bắt đầu attempt
            )
            answers.append(answer_detail)

        quiz_info = {
            "id": attempt.quiz_id.id,
            "name": attempt.quiz_id.name,
            "code": attempt.quiz_id.code,
            "max_score": attempt.quiz_id.max_score,
            "passing_score": attempt.quiz_id.passing_score,
            "show_correct_answers": attempt.quiz_id.show_correct_answers,
        }

        return ResponseBase(
            success=True,
            message="Lấy chi tiết attempt thành công",
            data=AttemptDetailResponse(
                attempt=attempt_detail,
                answers=answers,
                quiz_info=quiz_info
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi lấy chi tiết attempt {attempt_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "/attempts/{attempt_id}/grade",
    response_model=ResponseBase[Dict[str, Any]],
    summary="Chấm điểm attempt",
    description="Chấm điểm thủ công cho attempt (chủ yếu cho câu tự luận)"
)
def grade_attempt(
    attempt_id: int,
    request: GradeAttemptRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Chấm điểm attempt"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy attempt và kiểm tra quyền
        attempt = current_user.env["eb.quiz.attempt"].browse(attempt_id)
        if not attempt.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy attempt"
            )

        if attempt.quiz_id.instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền chấm điểm attempt này"
            )

        # Cập nhật điểm cho từng câu trả lời
        for answer_data in request.answers:
            answer_id = answer_data.get("answer_id")
            score = answer_data.get("score")
            feedback = answer_data.get("feedback")

            if answer_id and score is not None:
                answer = current_user.env["eb.quiz.attempt.answer"].browse(answer_id)
                if answer.exists() and answer.attempt_id.id == attempt.id:
                    answer.write({
                        "score": score,
                        "feedback": feedback,
                        "state": "graded",  # Thay needs_grading=False bằng state="graded"
                        "graded_by": instructor.id,
                        "graded_date": datetime.now(),  # Sửa graded_at thành graded_date theo model
                    })

        # Cập nhật feedback chung cho attempt
        if request.feedback:
            attempt.write({"feedback": request.feedback})

        # Tính lại tổng điểm
        total_score = sum(answer.score for answer in attempt.answer_ids if answer.score)

        # Cập nhật trạng thái và điểm số (percentage sẽ được tính tự động bởi computed field)
        attempt.write({
            "state": "graded",
            "score": total_score,
        })

        # Cập nhật result thông qua attempt method
        attempt._update_result(attempt)

        return ResponseBase(
            success=True,
            message="Chấm điểm thành công",
            data={
                "attempt_id": attempt.id,
                "final_score": attempt.score,
                "percentage": attempt.percentage,
                "is_passed": attempt.is_passed,
                "state": attempt.state,
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi chấm điểm attempt {attempt_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/grading/pending",
    response_model=ResponseBase[PendingGradingResponse],
    summary="Lấy danh sách bài cần chấm",
    description="Lấy danh sách tất cả attempt cần chấm điểm thủ công"
)
def get_pending_grading(
    page: int = Query(1, ge=1, description="Số trang"),
    limit: int = Query(50, ge=1, le=200, description="Số item mỗi trang"),
    quiz_id: Optional[int] = Query(None, description="Lọc theo quiz"),
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy danh sách bài cần chấm"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Build domain cho attempt search
        domain = [
            ("quiz_id.instructor_id", "=", instructor.id),  # Quiz của giảng viên này
            ("state", "=", "completed"),  # Đã hoàn thành = cần chấm điểm
        ]

        # Thêm filter theo quiz
        if quiz_id:
            domain.append(("quiz_id", "=", quiz_id))

        # Tính offset
        offset = (page - 1) * limit

        # Lấy attempts cần chấm
        attempts = current_user.env["eb.quiz.attempt"].search(
            domain,
            limit=limit,
            offset=offset,
            order="end_time asc"  # Ưu tiên bài nộp sớm nhất
        )

        total_count = current_user.env["eb.quiz.attempt"].search_count(domain)

        # Convert to schema
        attempt_items = []
        for attempt in attempts:
            attempt_schema = StudentAttemptDetailSchema(
                id=attempt.id,
                student_id=attempt.student_id.id,
                student_name=attempt.student_id.name,
                student_code=attempt.student_id.code,
                attempt_number=attempt.attempt_number,
                start_time=safe_value(attempt.start_time),
                end_time=safe_value(attempt.end_time),
                time_spent=safe_value(attempt.time_spent),
                score=attempt.score,
                max_score=attempt.max_score,
                percentage=attempt.percentage,
                is_passed=attempt.is_passed,
                state=attempt.state,
                needs_grading=(attempt.state == "completed"),  # completed = cần chấm điểm
                feedback=safe_value(attempt.feedback),
            )
            attempt_items.append(attempt_schema)

        # Thống kê theo quiz
        by_quiz = {}
        all_pending = current_user.env["eb.quiz.attempt"].search([
            ("quiz_id.instructor_id", "=", instructor.id),
            ("state", "=", "completed"),  # completed = cần chấm điểm
        ])

        for attempt in all_pending:
            quiz_name = attempt.quiz_id.name
            if quiz_name not in by_quiz:
                by_quiz[quiz_name] = 0
            by_quiz[quiz_name] += 1

        return ResponseBase(
            success=True,
            message=f"Tìm thấy {total_count} bài cần chấm điểm",
            data=PendingGradingResponse(
                attempts=attempt_items,
                total_count=total_count,
                by_quiz=by_quiz
            )
        )

    except Exception as e:
        _logger.error(f"Lỗi khi lấy danh sách bài cần chấm: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.post(
    "/answers/{answer_id}/grade",
    response_model=ResponseBase[Dict[str, Any]],
    summary="Chấm điểm câu trả lời",
    description="Chấm điểm thủ công cho một câu trả lời cụ thể (essay, fill_in_blank)"
)
def grade_answer(
    answer_id: int,
    request: GradeAnswerRequest,
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Chấm điểm câu trả lời"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Lấy answer và kiểm tra quyền
        answer = current_user.env["eb.quiz.attempt.answer"].browse(answer_id)
        if not answer.exists():
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy câu trả lời"
            )

        # Kiểm tra quyền thông qua class
        quiz = answer.attempt_id.quiz_id
        if quiz.class_id.primary_instructor_id.id != instructor.id:
            raise HTTPException(
                status_code=403,
                detail="Bạn không có quyền chấm điểm câu trả lời này"
            )

        # Cập nhật điểm và feedback
        answer.write({
            "score": request.score,
            "is_correct": request.is_correct,
            "feedback": request.feedback,
            "state": "graded",
            "graded_by": current_user.id,
            "graded_date": datetime.now(),
        })

        # Sử dụng method có sẵn để tự động cập nhật tổng điểm
        answer.action_save_grade()

        # Kiểm tra xem tất cả answers đã được chấm chưa
        attempt = answer.attempt_id
        pending_answers = attempt.answer_ids.filtered(lambda a: a.state == 'pending')

        # Nếu không còn answer nào pending, chuyển attempt state thành "graded"
        if not pending_answers:
            attempt.write({
                "state": "graded",
                "graded_by": current_user.id,
                "graded_date": datetime.now(),
            })
            message = "Chấm điểm thành công. Tất cả câu hỏi đã được chấm xong."
        else:
            message = f"Chấm điểm thành công. Còn {len(pending_answers)} câu chưa chấm."

        return ResponseBase(
            success=True,
            message=message,
            data={
                "answer_id": answer.id,
                "score": answer.score,
                "is_correct": answer.is_correct,
                "feedback": answer.feedback,
                "attempt_id": attempt.id,
                "attempt_state": attempt.state,
                "attempt_total_score": attempt.score,
                "attempt_percentage": attempt.percentage,
                "attempt_is_passed": attempt.is_passed,
                "pending_answers_count": len(pending_answers),
                "all_graded": len(pending_answers) == 0,
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Lỗi khi chấm điểm answer {answer_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )


@router.get(
    "/available-for-lesson",
    response_model=AvailableQuizzesResponse,
    summary="Lấy danh sách quiz có thể gán cho lesson",
    description="Lấy danh sách quiz của instructor có thể gán cho lesson"
)
def get_available_quizzes_for_lesson(
    subject_id: Optional[int] = Query(None, description="Lọc theo môn học"),
    current_user=Depends(get_current_user),
    _=Depends(require_instructor),
):
    """Lấy danh sách quiz có thể gán cho lesson"""
    try:
        # Tìm instructor record
        instructor = current_user.env["eb.instructor.instructor"].search([
            ("user_id", "=", current_user.id)
        ], limit=1)

        if not instructor:
            raise HTTPException(
                status_code=404,
                detail="Không tìm thấy thông tin giảng viên"
            )

        # Build domain cho quiz search
        domain = [
            ("instructor_id", "=", instructor.id),
            ("state", "=", "ready"),  # Chỉ quiz đã sẵn sàng
        ]

        # Thêm filter theo subject nếu có
        if subject_id:
            domain.append(("subject_id", "=", subject_id))

        # Lấy quizzes
        quizzes = current_user.env["eb.quiz"].search(
            domain,
            order="create_date desc"
        )

        # Convert to schema format
        quiz_items = []
        for quiz in quizzes:
            # Tính số buổi học đang công bố quiz này
            published_lesson_count = current_user.env["eb.lesson.quiz"].search_count([
                ("quiz_id", "=", quiz.id),
                ("is_active", "=", True)
            ])

            quiz_item = AvailableQuizSchema(
                id=quiz.id,
                name=quiz.name,
                code=safe_value(quiz.code) or f"QUIZ-{quiz.id}",
                quiz_type=quiz.quiz_type,
                state=quiz.state,
                question_count=quiz.question_count or 0,
                max_score=quiz.max_score,
                time_limit=safe_value(quiz.time_limit),
                passing_score=quiz.passing_score,
                subject_name=safe_value(quiz.subject_id.name) if quiz.subject_id else None,
                class_name=safe_value(quiz.class_id.name) if quiz.class_id else None,
                is_currently_published=published_lesson_count > 0,
                published_lesson_count=published_lesson_count,
            )
            quiz_items.append(quiz_item)

        return AvailableQuizzesResponse(
            success=True,
            message=f"Tìm thấy {len(quiz_items)} quiz có thể sử dụng",
            data=quiz_items
        )

    except Exception as e:
        _logger.error(f"Lỗi khi lấy danh sách quiz available: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi server: {str(e)}"
        )
