# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class QuizAnswerOptionSchema(BaseModel):
    """Schema cho lựa chọn đáp án"""
    id: int = Field(..., description="ID đáp án")
    text: str = Field(..., description="Nội dung đáp án")
    sequence: int = Field(..., description="Thứ tự hiển thị")


class QuizQuestionSchema(BaseModel):
    """Schema cho câu hỏi quiz"""
    id: int = Field(..., description="ID câu hỏi")
    name: str = Field(..., description="Nội dung câu hỏi")
    question_type: str = Field(..., description="Loại câu hỏi: multiple_choice|single_choice|true_false|essay|matching|fill_in_blank")
    score: float = Field(..., description="Điểm số câu hỏi")
    difficulty: str = Field(..., description="Độ khó: easy|medium|hard")
    sequence: int = Field(..., description="Thứ tự câu hỏi")
    image: Optional[str] = Field(None, description="Hình ảnh minh họa (base64)")
    explanation: Optional[str] = Field(None, description="Giải thích đáp án")
    is_required: bool = Field(True, description="Bắt buộc trả lời")
    
    # Đáp án cho câu hỏi trắc nghiệm
    answer_options: Optional[List[QuizAnswerOptionSchema]] = Field(None, description="Các lựa chọn đáp án")


class QuizDetailSchema(BaseModel):
    """Schema cho chi tiết quiz"""
    id: int = Field(..., description="ID quiz")
    name: str = Field(..., description="Tên quiz")
    code: str = Field(..., description="Mã quiz")
    description: Optional[str] = Field(None, description="Mô tả quiz")
    instruction: Optional[str] = Field(None, description="Hướng dẫn làm bài")
    quiz_type: str = Field(..., description="Loại quiz: quiz|assignment|midterm|final|project")
    
    # Thông tin môn học và lớp
    subject_name: Optional[str] = Field(None, description="Tên môn học")
    course_name: Optional[str] = Field(None, description="Tên khóa học")
    class_name: Optional[str] = Field(None, description="Tên lớp học")
    instructor_name: Optional[str] = Field(None, description="Tên giảng viên")
    
    # Cài đặt quiz
    max_score: float = Field(..., description="Điểm tối đa")
    passing_score: float = Field(..., description="Điểm đạt")
    time_limit: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    
    # Thời gian
    start_date: Optional[datetime] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[datetime] = Field(None, description="Ngày kết thúc")
    
    # Cài đặt hiển thị
    is_randomized: bool = Field(False, description="Ngẫu nhiên thứ tự câu hỏi")
    show_correct_answers: bool = Field(False, description="Hiển thị đáp án đúng")
    show_result_immediately: bool = Field(True, description="Hiển thị kết quả ngay")
    
    # Trạng thái
    state: str = Field(..., description="Trạng thái: draft|published|closed")
    is_published: bool = Field(False, description="Đã công bố")
    
    # Thống kê
    question_count: int = Field(0, description="Số lượng câu hỏi")
    attempt_count: int = Field(0, description="Số lần làm bài")
    student_count: int = Field(0, description="Số học viên đã làm")
    average_score: float = Field(0.0, description="Điểm trung bình")
    pass_rate: float = Field(0.0, description="Tỷ lệ đạt")
    
    # Danh sách câu hỏi (chỉ khi làm bài)
    questions: Optional[List[QuizQuestionSchema]] = Field(None, description="Danh sách câu hỏi")

    # Thông tin chi tiết về attempt status (optional) - will be set after AttemptStatusSchema is defined
    attempt_status: Optional["AttemptStatusSchema"] = Field(None, description="Trạng thái attempt chi tiết")


class InProgressAttemptSchema(BaseModel):
    """Schema cho attempt đang in_progress"""
    id: int = Field(..., description="ID attempt")
    attempt_number: int = Field(..., description="Số lần thử")
    start_time: datetime = Field(..., description="Thời gian bắt đầu")
    time_spent: Optional[int] = Field(None, description="Thời gian đã làm (phút)")


class AttemptOptionsSchema(BaseModel):
    """Schema cho các tùy chọn có thể thực hiện với attempt"""
    can_continue: bool = Field(..., description="Có thể tiếp tục attempt hiện tại")
    can_restart: bool = Field(..., description="Có thể làm lại từ đầu")
    can_start_new: bool = Field(..., description="Có thể bắt đầu attempt mới")
    restart_warning: Optional[str] = Field(None, description="Cảnh báo khi làm lại")


class FileAttachmentSchema(BaseModel):
    """Schema cho file đính kèm"""
    id: int = Field(..., description="ID attachment")
    name: str = Field(..., description="Tên file")
    size: int = Field(..., description="Kích thước file (bytes)")
    mimetype: str = Field(..., description="Loại file")
    access_token: str = Field(..., description="Token để truy cập file")
    url: str = Field(..., description="URL để tải file")
    preview_url: Optional[str] = Field(None, description="URL preview (nếu có)")
    uploaded_at: datetime = Field(..., description="Thời gian upload")


class AttemptStatusSchema(BaseModel):
    """Schema cho trạng thái attempt của student"""
    has_in_progress: bool = Field(..., description="Có attempt đang làm dở")
    in_progress_attempt: Optional[InProgressAttemptSchema] = Field(None, description="Thông tin attempt đang dở")
    total_attempts: int = Field(0, description="Tổng số attempts")
    completed_attempts: int = Field(0, description="Số attempts đã hoàn thành")
    best_score: Optional[float] = Field(None, description="Điểm cao nhất")
    last_attempt_date: Optional[datetime] = Field(None, description="Lần thử cuối")
    options: AttemptOptionsSchema = Field(..., description="Các tùy chọn có thể thực hiện")


class QuizListItemSchema(BaseModel):
    """Schema cho item trong danh sách quiz"""
    id: int = Field(..., description="ID quiz")
    name: str = Field(..., description="Tên quiz")
    code: str = Field(..., description="Mã quiz")
    quiz_type: str = Field(..., description="Loại quiz")
    subject_name: Optional[str] = Field(None, description="Tên môn học")
    class_name: Optional[str] = Field(None, description="Tên lớp học")
    instructor_name: Optional[str] = Field(None, description="Tên giảng viên")

    max_score: float = Field(..., description="Điểm tối đa")
    passing_score: float = Field(..., description="Điểm đạt")
    time_limit: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    question_count: int = Field(0, description="Số lượng câu hỏi")

    start_date: Optional[datetime] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[datetime] = Field(None, description="Ngày kết thúc")
    state: str = Field(..., description="Trạng thái")

    # Thông tin của học viên
    student_attempt_count: int = Field(0, description="Số lần học viên đã làm")
    student_best_score: Optional[float] = Field(None, description="Điểm cao nhất của học viên")
    student_last_attempt: Optional[datetime] = Field(None, description="Lần làm bài cuối")
    can_attempt: bool = Field(True, description="Có thể làm bài không")

    # Thông tin chi tiết về attempt status (optional) - will be set after AttemptStatusSchema is defined
    attempt_status: Optional["AttemptStatusSchema"] = Field(None, description="Trạng thái attempt chi tiết")


class QuizAttemptAnswerSchema(BaseModel):
    """Schema cho câu trả lời trong attempt"""
    question_id: int = Field(..., description="ID câu hỏi")
    question_type: str = Field(..., description="Loại câu hỏi")
    
    # Đáp án cho câu hỏi trắc nghiệm
    answer_id: Optional[int] = Field(None, description="ID đáp án đã chọn (single choice)")
    answer_ids: Optional[List[int]] = Field(None, description="Danh sách ID đáp án đã chọn (multiple choice)")
    
    # Đáp án cho câu hỏi tự luận
    text_answer: Optional[str] = Field(None, description="Câu trả lời dạng text")

    # File attachments cho câu hỏi tự luận
    attachment_ids: Optional[List[int]] = Field(None, description="Danh sách ID file đính kèm")
    attachments: Optional[List[FileAttachmentSchema]] = Field(None, description="Thông tin file đính kèm")

    # Thời gian trả lời
    answered_at: Optional[datetime] = Field(None, description="Thời gian trả lời")


class QuizFileUploadRequest(BaseModel):
    """Schema cho upload file trong quiz"""
    question_id: int = Field(..., description="ID câu hỏi")
    attempt_id: int = Field(..., description="ID attempt")
    description: Optional[str] = Field(None, description="Mô tả file")


class StartQuizAttemptRequest(BaseModel):
    """Schema cho request bắt đầu làm bài"""
    quiz_id: int = Field(..., description="ID quiz")


class SubmitQuizAttemptRequest(BaseModel):
    """Schema cho request nộp bài"""
    answers: List[QuizAttemptAnswerSchema] = Field(..., description="Danh sách câu trả lời")
    submit_time: datetime = Field(..., description="Thời gian nộp bài")


class QuizAttemptSchema(BaseModel):
    """Schema cho attempt"""
    id: int = Field(..., description="ID attempt")
    quiz_id: int = Field(..., description="ID quiz")
    quiz_name: str = Field(..., description="Tên quiz")
    attempt_number: int = Field(..., description="Số lần thử")
    
    start_time: datetime = Field(..., description="Thời gian bắt đầu")
    end_time: Optional[datetime] = Field(None, description="Thời gian kết thúc")
    time_spent: Optional[int] = Field(None, description="Thời gian làm bài (phút)")
    remaining_time: Optional[int] = Field(None, description="Thời gian còn lại (phút)")
    
    score: Optional[float] = Field(None, description="Điểm số")
    max_score: float = Field(..., description="Điểm tối đa")
    percentage: Optional[float] = Field(None, description="Phần trăm")
    is_passed: Optional[bool] = Field(None, description="Đã đạt")
    
    state: str = Field(..., description="Trạng thái: in_progress|completed|graded")
    feedback: Optional[str] = Field(None, description="Nhận xét")
    
    # Câu trả lời (chỉ khi cần thiết)
    answers: Optional[List[QuizAttemptAnswerSchema]] = Field(None, description="Danh sách câu trả lời")


class QuizResultSchema(BaseModel):
    """Schema cho kết quả quiz"""
    id: int = Field(..., description="ID kết quả")
    quiz_id: int = Field(..., description="ID quiz")
    quiz_name: str = Field(..., description="Tên quiz")
    
    attempt_count: int = Field(..., description="Số lần đã làm")
    final_score: float = Field(..., description="Điểm cuối cùng")
    max_score: float = Field(..., description="Điểm tối đa")
    percentage: float = Field(..., description="Phần trăm")
    is_passed: bool = Field(..., description="Đã đạt")
    
    completion_date: Optional[datetime] = Field(None, description="Ngày hoàn thành")
    state: str = Field(..., description="Trạng thái")
    
    best_attempt_id: Optional[int] = Field(None, description="ID attempt tốt nhất")


class StudentQuizzesResponse(BaseModel):
    """Schema cho response danh sách quiz của học viên"""
    quizzes: List[QuizListItemSchema] = Field(..., description="Danh sách quiz")
    total_count: int = Field(..., description="Tổng số quiz")
    page: int = Field(..., description="Trang hiện tại")
    limit: int = Field(..., description="Số item mỗi trang")


class StudentQuizAttemptsResponse(BaseModel):
    """Schema cho response danh sách attempt của học viên"""
    attempts: List[QuizAttemptSchema] = Field(..., description="Danh sách attempt")
    total_count: int = Field(..., description="Tổng số attempt")


class StudentQuizResultsResponse(BaseModel):
    """Schema cho response danh sách kết quả của học viên"""
    results: List[QuizResultSchema] = Field(..., description="Danh sách kết quả")
    total_count: int = Field(..., description="Tổng số kết quả")


# Resolve forward references
QuizDetailSchema.model_rebuild()
QuizListItemSchema.model_rebuild()
