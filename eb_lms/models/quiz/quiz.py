# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class Quiz(models.Model):
    """
    Đề kiểm tra trong hệ thống LMS.

    Đề kiểm tra là tập hợp các câu hỏi được sử dụng để đánh giá kiến thức và kỹ năng
    của học viên. <PERSON><PERSON> kiểm tra có thể là bài kiểm tra nhanh, bài thi gi<PERSON><PERSON> kỳ, bài thi cuối kỳ,
    bài tập, hoặc dự án.
    """

    _name = "eb.quiz"
    _description = "Quiz"
    _inherit = ["mail.thread", "mail.activity.mixin", "eb.sequence.mixin"]
    _order = "create_date desc, name"

    # Constraints
    _sql_constraints = [
        ('check_max_score_positive', 'CHECK(max_score > 0)', 'Điểm tối đa phải lớn hơn 0.'),
        ('check_passing_score_valid', 'CHECK(passing_score >= 0 AND passing_score <= max_score)', 'Điểm đạt phải từ 0 đến điểm tối đa.'),
        ('check_time_limit_positive', 'CHECK(time_limit IS NULL OR time_limit > 0)', 'Thời gian làm bài phải lớn hơn 0 (nếu có).'),
    ]

    name = fields.Char(
        string="Tên đề kiểm tra",
        required=True,
        tracking=True,
        help="Tên của đề kiểm tra",
    )

    code = fields.Char(
        string="Mã đề kiểm tra",
        required=True,
        tracking=True,
        copy=False,
        readonly=True,
        default=lambda self: _("New"),
        help="Mã duy nhất của đề kiểm tra",
    )

    description = fields.Html(
        string="Mô tả",
        help="Mô tả chi tiết về đề kiểm tra",
    )

    instruction = fields.Html(
        string="Hướng dẫn làm bài",
        help="Hướng dẫn chi tiết cho học viên về cách làm bài",
    )

    quiz_type = fields.Selection(
        [
            ("quiz", "Bài kiểm tra nhanh"),
            ("assignment", "Bài tập"),
            ("midterm", "Thi giữa kỳ"),
            ("final", "Thi cuối kỳ"),
            ("project", "Dự án"),
        ],
        string="Loại kiểm tra",
        default="quiz",
        required=True,
        tracking=True,
        help="Loại của đề kiểm tra",
    )

    subject_id = fields.Many2one(
        "eb.subject.subject",
        string="Môn học",
        tracking=True,
        help="Môn học liên quan đến đề kiểm tra",
    )

    course_id = fields.Many2one(
        "eb.course.course",
        string="Khóa học",
        tracking=True,
        help="Khóa học liên quan đến đề kiểm tra",
    )

    class_id = fields.Many2one(
        "eb.class.class",
        string="Lớp học",
        tracking=True,
        help="Lớp học liên quan đến đề kiểm tra",
    )

    instructor_id = fields.Many2one(
        "eb.instructor.instructor",
        string="Giảng viên phụ trách",
        tracking=True,
        help="Giảng viên phụ trách đề kiểm tra",
    )

    # New design with lesson-quiz junction
    lesson_quiz_ids = fields.One2many(
        "eb.lesson.quiz",
        "quiz_id",
        string="Lesson-Quiz Relationships",
        help="Các mối quan hệ với buổi học",
    )

    lesson_ids = fields.Many2many(
        "eb.lesson.lesson",
        string="Buổi học",
        compute="_compute_lessons",
        help="Các buổi học đã gán quiz này",
    )

    active_lesson_ids = fields.Many2many(
        "eb.lesson.lesson",
        string="Buổi học đang hoạt động",
        compute="_compute_active_lessons",
        help="Các buổi học đang công bố quiz này",
    )

    # New computed fields for publish status
    is_currently_published = fields.Boolean(
        string="Đang được công bố",
        compute="_compute_publish_status",
        store=True,
        help="Quiz có đang được công bố ở ít nhất 1 buổi học không",
    )

    published_lesson_count = fields.Integer(
        string="Số buổi học đang công bố",
        compute="_compute_publish_status",
        store=True,
        help="Số lượng buổi học đang công bố quiz này",
    )

    max_score = fields.Float(
        string="Điểm tối đa",
        default=100.0,
        required=True,
        tracking=True,
        help="Điểm tối đa của đề kiểm tra",
    )

    passing_score = fields.Float(
        string="Điểm đạt",
        default=50.0,
        tracking=True,
        help="Điểm tối thiểu để đạt đề kiểm tra",
    )

    time_limit = fields.Integer(
        string="Thời gian làm bài (phút)",
        tracking=True,
        help="Thời gian tối đa cho phép làm bài (tính bằng phút)",
    )

    # DEPRECATED: Use lesson_quiz.is_active instead
    is_published = fields.Boolean(
        string="Đã công bố (deprecated)",
        default=False,
        tracking=True,
        help="DEPRECATED: Sử dụng lesson_quiz.is_active thay thế. Field này sẽ bị xóa trong version tương lai.",
    )

    # DEPRECATED: Use lesson_quiz.published_at instead
    publish_date = fields.Datetime(
        string="Ngày công bố (deprecated)",
        tracking=True,
        help="DEPRECATED: Sử dụng lesson_quiz.published_at thay thế. Field này sẽ bị xóa trong version tương lai.",
    )

    start_date = fields.Datetime(
        string="Ngày bắt đầu",
        tracking=True,
        help="Ngày giờ bắt đầu cho phép làm bài",
    )

    end_date = fields.Datetime(
        string="Ngày kết thúc",
        tracking=True,
        help="Ngày giờ kết thúc cho phép làm bài",
    )

    is_randomized = fields.Boolean(
        string="Ngẫu nhiên thứ tự câu hỏi",
        default=False,
        help="Có ngẫu nhiên thứ tự câu hỏi khi hiển thị cho học viên không",
    )

    show_correct_answers = fields.Boolean(
        string="Hiển thị đáp án đúng",
        default=False,
        help="Có hiển thị đáp án đúng sau khi học viên làm bài không",
    )

    show_result_immediately = fields.Boolean(
        string="Hiển thị kết quả ngay",
        default=True,
        help="Có hiển thị kết quả ngay sau khi học viên làm bài không",
    )

    state = fields.Selection(
        [
            ("draft", "Nháp"),
            ("ready", "Sẵn sàng"),
            ("archived", "Đã lưu trữ"),
        ],
        string="Trạng thái",
        default="draft",
        tracking=True,
        help="Trạng thái của quiz content (không liên quan đến publish). Publish được quản lý ở lesson_quiz.",
    )

    active = fields.Boolean(
        string="Đang hoạt động",
        default=True,
        help="Nếu không được chọn, đề kiểm tra sẽ bị ẩn khỏi các danh sách",
    )

    # Relationships
    question_ids = fields.One2many(
        "eb.quiz.question",
        "quiz_id",
        string="Câu hỏi",
        help="Danh sách các câu hỏi trong đề kiểm tra",
    )

    attempt_ids = fields.One2many(
        "eb.quiz.attempt",
        "quiz_id",
        string="Lần làm bài",
        help="Danh sách các lần làm bài của học viên",
    )

    result_ids = fields.One2many(
        "eb.quiz.result",
        "quiz_id",
        string="Kết quả",
        help="Danh sách kết quả của học viên",
    )

    analytics_ids = fields.One2many(
        "eb.quiz.analytics",
        "quiz_id",
        string="Phân tích",
        help="Phân tích kết quả đề kiểm tra",
    )

    question_statistics_ids = fields.One2many(
        "eb.quiz.question.statistics",
        "quiz_id",
        string="Thống kê câu hỏi",
        help="Thống kê về các câu hỏi trong đề kiểm tra",
    )

    # Computed fields
    question_count = fields.Integer(
        string="Số lượng câu hỏi",
        compute="_compute_question_count",
        store=True,
        help="Số lượng câu hỏi trong đề kiểm tra",
    )

    attempt_count = fields.Integer(
        string="Số lần làm bài",
        compute="_compute_attempt_count",
        help="Tổng số lần làm bài của học viên",
    )

    student_count = fields.Integer(
        string="Số học viên",
        compute="_compute_student_count",
        help="Số học viên đã làm bài",
    )

    average_score = fields.Float(
        string="Điểm trung bình",
        compute="_compute_average_score",
        help="Điểm trung bình của tất cả các lần làm bài",
    )

    pass_rate = fields.Float(
        string="Tỷ lệ đạt",
        compute="_compute_pass_rate",
        help="Tỷ lệ học viên đạt đề kiểm tra",
    )

    @api.depends("lesson_quiz_ids")
    def _compute_lessons(self):
        for quiz in self:
            quiz.lesson_ids = quiz.lesson_quiz_ids.mapped('lesson_id')

    @api.depends("lesson_quiz_ids.is_active")
    def _compute_active_lessons(self):
        for quiz in self:
            active_lesson_quizzes = quiz.lesson_quiz_ids.filtered('is_active')
            quiz.active_lesson_ids = active_lesson_quizzes.mapped('lesson_id')

    @api.depends("lesson_quiz_ids.is_active")
    def _compute_publish_status(self):
        for quiz in self:
            active_lesson_quizzes = quiz.lesson_quiz_ids.filtered('is_active')
            quiz.is_currently_published = len(active_lesson_quizzes) > 0
            quiz.published_lesson_count = len(active_lesson_quizzes)

    @api.depends("question_ids")
    def _compute_question_count(self):
        for quiz in self:
            quiz.question_count = len(quiz.question_ids)

    @api.depends("attempt_ids")
    def _compute_attempt_count(self):
        for quiz in self:
            quiz.attempt_count = len(quiz.attempt_ids)

    @api.depends("result_ids")
    def _compute_student_count(self):
        for quiz in self:
            quiz.student_count = len(quiz.result_ids)

    @api.depends("attempt_ids.score", "attempt_ids.max_score")
    def _compute_average_score(self):
        for quiz in self:
            attempts = quiz.attempt_ids.filtered(lambda a: a.state == "graded")
            if attempts:
                quiz.average_score = sum(a.score for a in attempts) / len(attempts)
            else:
                quiz.average_score = 0.0

    @api.depends("result_ids.is_passed")
    def _compute_pass_rate(self):
        for quiz in self:
            if quiz.result_ids:
                passed_count = len(quiz.result_ids.filtered(lambda r: r.is_passed))
                quiz.pass_rate = (passed_count / len(quiz.result_ids)) * 100
            else:
                quiz.pass_rate = 0.0

    @api.model_create_multi
    def create(self, vals_list):
        """Override để tự động tạo mã đề kiểm tra."""
        for vals in vals_list:
            if vals.get("code", _("New")) == _("New"):
                vals["code"] = self._generate_unique_code_with_fallback('quiz')

        # Tạo quiz với tối ưu performance
        quizzes = super(Quiz, self).create(vals_list)

        return quizzes

    def write(self, vals):
        """Override để validate state changes."""
        # Validate khi chuyển state sang 'ready'
        if vals.get('state') == 'ready':
            for quiz in self:
                if not quiz.question_ids:
                    raise ValidationError(_("Không thể đánh dấu quiz '%s' là sẵn sàng vì chưa có câu hỏi nào.") % quiz.name)

        return super(Quiz, self).write(vals)

    @api.constrains('state', 'question_ids')
    def _check_ready_state_has_questions(self):
        """Đảm bảo quiz ở state 'ready' phải có ít nhất 1 câu hỏi."""
        for quiz in self:
            if quiz.state == 'ready' and not quiz.question_ids:
                raise ValidationError(_("Quiz '%s' không thể ở trạng thái 'Sẵn sàng' khi chưa có câu hỏi nào.") % quiz.name)

    @api.constrains('start_date', 'end_date')
    def _check_date_consistency(self):
        """Đảm bảo ngày kết thúc sau ngày bắt đầu."""
        for quiz in self:
            if quiz.start_date and quiz.end_date and quiz.start_date >= quiz.end_date:
                raise ValidationError(_("Ngày kết thúc phải sau ngày bắt đầu trong quiz '%s'.") % quiz.name)

    # DEPRECATED METHODS - Use lesson_quiz actions instead
    def action_publish(self):
        """
        DEPRECATED: Sử dụng lesson_quiz.action_publish() thay thế.
        Method này chỉ set state = 'ready' để backward compatibility.
        """
        for quiz in self:
            if not quiz.question_ids:
                raise ValidationError(_("Không thể đánh dấu sẵn sàng cho đề kiểm tra không có câu hỏi."))
            quiz.write({
                "state": "ready",
                # Không set is_published nữa vì logic đã chuyển sang lesson_quiz
            })
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Thông báo',
                'message': 'Quiz đã sẵn sàng. Để công bố cho học viên, vui lòng sử dụng chức năng "Quản lý Quiz" trong buổi học.',
                'sticky': False,
                'type': 'info',
            },
        }

    def action_close(self):
        """DEPRECATED: Sử dụng lesson_quiz.action_unpublish() thay thế."""
        for quiz in self:
            quiz.write({"state": "archived"})
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Thông báo',
                'message': 'Quiz đã được lưu trữ. Để hủy công bố cho học viên, vui lòng sử dụng chức năng "Quản lý Quiz" trong buổi học.',
                'sticky': False,
                'type': 'info',
            },
        }

    def action_reset_to_draft(self):
        """Reset quiz state to draft."""
        for quiz in self:
            quiz.write({"state": "draft"})
        return True

    def action_view_questions(self):
        """Xem danh sách câu hỏi."""
        self.ensure_one()
        return {
            "name": _("Câu hỏi"),
            "type": "ir.actions.act_window",
            "res_model": "eb.quiz.question",
            "view_mode": "list,form",
            "domain": [("quiz_id", "=", self.id)],
            "context": {"default_quiz_id": self.id},
        }

    def action_view_attempts(self):
        """Xem danh sách lần làm bài."""
        self.ensure_one()
        return {
            "name": _("Lần làm bài"),
            "type": "ir.actions.act_window",
            "res_model": "eb.quiz.attempt",
            "view_mode": "list,form",
            "domain": [("quiz_id", "=", self.id)],
            "context": {"default_quiz_id": self.id},
        }

    def action_view_results(self):
        """Xem danh sách kết quả."""
        self.ensure_one()
        return {
            "name": _("Kết quả"),
            "type": "ir.actions.act_window",
            "res_model": "eb.quiz.result",
            "view_mode": "list,form",
            "domain": [("quiz_id", "=", self.id)],
            "context": {"default_quiz_id": self.id},
        }

    def action_view_analytics(self):
        """Xem phân tích kết quả."""
        self.ensure_one()
        return {
            "name": _("Phân tích"),
            "type": "ir.actions.act_window",
            "res_model": "eb.quiz.analytics",
            "view_mode": "list,form",
            "domain": [("quiz_id", "=", self.id)],
            "context": {"default_quiz_id": self.id},
        }

    def action_calculate_statistics(self):
        """Tính toán lại thống kê."""
        for quiz in self:
            # Tính toán thống kê cho từng câu hỏi
            for question in quiz.question_ids:
                stats = self.env["eb.quiz.question.statistics"].search([
                    ("quiz_id", "=", quiz.id),
                    ("question_id", "=", question.id),
                ])

                if not stats:
                    stats = self.env["eb.quiz.question.statistics"].create({
                        "quiz_id": quiz.id,
                        "question_id": question.id,
                    })

                # Tính toán số lần trả lời đúng, sai, bỏ qua
                attempt_answers = self.env["eb.quiz.attempt.answer"].search([
                    ("question_id", "=", question.id),
                    ("attempt_id.quiz_id", "=", quiz.id),
                ])

                correct_count = len(attempt_answers.filtered(lambda a: a.is_correct))
                incorrect_count = len(attempt_answers.filtered(lambda a: not a.is_correct and a.state != "skipped"))
                skip_count = len(attempt_answers.filtered(lambda a: a.state == "skipped"))

                # Tính điểm trung bình
                if attempt_answers:
                    average_score = sum(a.score for a in attempt_answers) / len(attempt_answers)
                else:
                    average_score = 0.0

                # Tính chỉ số độ khó (tỷ lệ trả lời đúng)
                if correct_count + incorrect_count > 0:
                    difficulty_index = correct_count / (correct_count + incorrect_count)
                else:
                    difficulty_index = 0.0

                # Cập nhật thống kê
                stats.write({
                    "correct_count": correct_count,
                    "incorrect_count": incorrect_count,
                    "skip_count": skip_count,
                    "average_score": average_score,
                    "difficulty_index": difficulty_index,
                    "last_calculated": fields.Datetime.now(),
                })

            # Tính toán thống kê tổng thể
            analytics = self.env["eb.quiz.analytics"].search([
                ("quiz_id", "=", quiz.id),
                ("class_id", "=", quiz.class_id.id if quiz.class_id else False),
            ])

            if not analytics:
                analytics = self.env["eb.quiz.analytics"].create({
                    "quiz_id": quiz.id,
                    "class_id": quiz.class_id.id if quiz.class_id else False,
                })

            # Cập nhật thống kê tổng thể
            analytics.write({
                "attempt_count": quiz.attempt_count,
                "student_count": quiz.student_count,
                "average_score": quiz.average_score,
                "pass_rate": quiz.pass_rate,
                "last_calculated": fields.Datetime.now(),
            })

        return True
