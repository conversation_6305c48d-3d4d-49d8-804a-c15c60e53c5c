# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Tests for eb.quiz model.

This module contains comprehensive tests for the Quiz model including:
- CRUD operations
- Question management
- Attempt tracking
- Scoring logic
- Time limits
- Access control
"""

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
from ..common import EbLmsTestCase, TestDataMixin, AssertionMixin
from ..factories import QuizFactory, CourseFactory, StudentFactory

class TestQuizModel(EbLmsTestCase, TestDataMixin, AssertionMixin):
    """Test cases for eb.quiz model."""

    @classmethod
    def setUpClass(cls):
        """Set up test data."""

    def test_quiz_creation_minimal(self):
        """Test creating quiz with minimal required fields."""
            'name': 'Python Basics Quiz',
            'course_id': self.course.id,
        })
        
        self.assertTrue(quiz.id)
        self.assertEqual(quiz.name, 'Python Basics Quiz')
        self.assertEqual(quiz.course_id, self.course)
        self.assertTrue(quiz.is_active)  # Default value

    def test_quiz_creation_full(self):
        """Test creating quiz with all fields."""
        )
        
        self.assertEqual(quiz.name, 'Advanced Python Quiz')
        self.assertEqual(quiz.description, 'Comprehensive Python assessment')
        self.assertEqual(quiz.time_limit, 90)
        self.assertEqual(quiz.passing_score, 80.0)
        self.assertTrue(quiz.is_active)
        self.assertTrue(quiz.randomize_questions)
        self.assertFalse(quiz.show_results_immediately)

    def test_quiz_time_limit_validation(self):
        """Test time limit validation."""
        
        self.assertEqual(quiz.time_limit, 60)
        
        self.assertEqual(quiz.time_limit, 0)
        
        with self.assertRaises(ValidationError):

    def test_quiz_passing_score_validation(self):
        """Test passing score validation."""
        
        self.assertEqual(quiz.passing_score, 75.0)
        
        self.assertEqual(quiz.passing_score, 0.0)
        
        self.assertEqual(quiz.passing_score, 100.0)
        
        with self.assertRaises(ValidationError):
        
        with self.assertRaises(ValidationError):

    def test_quiz_question_count(self):
        """Test question count computation."""
        
        self.assertEqual(quiz.question_count, 0)
        
        # Add questions (would be tested in question tests)
        # This tests the computed field structure

    def test_quiz_attempt_count(self):
        """Test attempt count computation."""
        
        self.assertEqual(quiz.attempt_count, 0)
        
        # Add attempts (would be tested in attempt tests)
        # This tests the computed field structure

    def test_quiz_average_score(self):
        """Test average score computation."""
        
        self.assertEqual(quiz.average_score, 0.0)
        
        # With attempts, average should be computed
        # This would be tested with actual attempt data

    def test_quiz_activation_deactivation(self):
        """Test quiz activation and deactivation."""
        )
        
        self.assertFalse(quiz.is_active)
        
        self.assertTrue(quiz.is_active)

    def test_quiz_student_can_attempt(self):
        """Test if student can attempt quiz."""
        )
        
            self.assertTrue(can_attempt)

    def test_quiz_inactive_attempt_prevention(self):
        """Test prevention of attempts on inactive quiz."""
        )
        
            self.assertFalse(can_attempt)

    def test_quiz_search_by_course(self):
        """Test searching quizzes by course."""

        ])
        
        self.assertIn(quiz1, course1_quizzes)
        self.assertNotIn(quiz2, course1_quizzes)

    def test_quiz_search_by_active_status(self):
        """Test searching quizzes by active status."""
        )
        )
        
        ])
        
        self.assertIn(quiz1, active_quizzes)
        self.assertNotIn(quiz2, active_quizzes)

    def test_quiz_copy(self):
        """Test copying a quiz."""
        )

        self.assertNotEqual(original.id, copy.id)
        self.assertEqual(copy.name, 'Original Quiz (copy)')
        self.assertEqual(copy.description, original.description)
        self.assertEqual(copy.time_limit, original.time_limit)
        self.assertEqual(copy.passing_score, original.passing_score)
        self.assertEqual(copy.course_id, original.course_id)

    def test_quiz_unlink(self):
        """Test deleting a quiz."""

        ])
        self.assertFalse(deleted_quiz)

    def test_quiz_with_attempts_unlink_restriction(self):
        """Test that quiz with attempts cannot be deleted."""
        
        # Create attempt record (would be done in attempt tests)
        # This tests the constraint if it exists
        
            self.assertTrue(quiz.exists())

    def test_quiz_ordering(self):
        """Test quiz ordering."""
        )
        )
        )
        
            ('id', 'in', [quiz1.id, quiz2.id, quiz3.id])
        ])
        
        self.assertEqual(list(quizzes), expected_order)

    def test_quiz_statistics_computation(self):
        """Test quiz statistics computation."""
        
        # Test various statistics if implemented
            self.assertEqual(quiz.completion_rate, 0.0)
        
            self.assertEqual(quiz.pass_rate, 0.0)

    def test_quiz_time_tracking(self):
        """Test quiz time tracking features."""
        )
        
        self.assertEqual(quiz.time_limit_seconds, 3600)  # 60 * 60

    def test_quiz_randomization_settings(self):
        """Test quiz randomization settings."""
        )
        
        self.assertTrue(quiz.randomize_questions)
        
        # Test question order generation if implemented
