# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Test data factories for EB LMS module.

This module provides factory classes for creating test data using the factory pattern.
Factories help create consistent, reusable test data with sensible defaults.
"""

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import random
import string


class BaseFactory:
    """Base factory class with common utilities."""
    
    @staticmethod
    def random_string(length=8):
        """Generate a random string of given length."""
        return ''.join(random.choices(string.ascii_lowercase, k=length))
    
    @staticmethod
    def random_email(domain="test.com"):
        """Generate a random email address."""
        username = BaseFactory.random_string(10)
        return f"{username}@{domain}"
    
    @staticmethod
    def random_phone():
        """Generate a random Vietnamese phone number."""
        return f"+8490{random.randint(1000000, 9999999)}"


class PartnerFactory(BaseFactory):
    """Factory for creating res.partner records."""
    
    @classmethod
    def create(cls, env, **kwargs):
        """Create a partner with default values."""
        defaults = {
            'name': f'Test Partner {cls.random_string(5)}',
            'email': cls.random_email(),
            'phone': cls.random_phone(),
            'is_company': False,
            'country_id': env.ref('base.vn').id,
        }
        defaults.update(kwargs)
        return env['res.partner'].create(defaults)


class CourseFactory(BaseFactory):
    """Factory for creating eb.course.course records."""
    
    @classmethod
    def create(cls, env, **kwargs):
        """Create a course with default values."""
        defaults = {
            'name': f'Test Course {cls.random_string(5)}',
            'description': 'Test course description',
            'duration_hours': 40,
            'max_students': 20,
            'price': 1000000.0,
            'currency_id': env.ref('base.VND').id,
        }
        defaults.update(kwargs)
        return env['eb.course.course'].create(defaults)


class ClassFactory(BaseFactory):
    """Factory for creating eb.class.class records."""
    
    @classmethod
    def create(cls, env, course=None, **kwargs):
        """Create a class with default values."""
        if not course:
            course = CourseFactory.create(env)
            
        today = datetime.now().date()
        defaults = {
            'name': f'Test Class {cls.random_string(5)}',
            'course_id': course.id,
            'start_date': today + timedelta(days=7),
            'end_date': today + relativedelta(months=2),
            'max_students': 15,
            'schedule_type': 'fixed',
        }
        defaults.update(kwargs)
        return env['eb.class.class'].create(defaults)


class StudentFactory(BaseFactory):
    """Factory for creating eb.student.student records."""
    
    @classmethod
    def create(cls, env, partner=None, **kwargs):
        """Create a student with default values."""
        if not partner:
            partner = PartnerFactory.create(env)
            
        defaults = {
            'name': partner.name,
            'partner_id': partner.id,
            'email': partner.email,
            'phone': partner.phone,
            'birth_date': datetime.now().date() - relativedelta(years=20),
            'gender': 'other',
            'status': 'active',
        }
        defaults.update(kwargs)
        return env['eb.student.student'].create(defaults)


class InstructorFactory(BaseFactory):
    """Factory for creating eb.instructor.instructor records."""
    
    @classmethod
    def create(cls, env, partner=None, **kwargs):
        """Create an instructor with default values."""
        if not partner:
            partner = PartnerFactory.create(env)
            
        defaults = {
            'name': partner.name,
            'partner_id': partner.id,
            'email': partner.email,
            'phone': partner.phone,
            'hire_date': datetime.now().date(),
            'status': 'active',
            'employee_type': 'full_time',
        }
        defaults.update(kwargs)
        return env['eb.instructor.instructor'].create(defaults)


class LessonFactory(BaseFactory):
    """Factory for creating eb.lesson.lesson records."""
    
    @classmethod
    def create(cls, env, class_obj=None, **kwargs):
        """Create a lesson with default values."""
        if not class_obj:
            class_obj = ClassFactory.create(env)
            
        today = datetime.now()
        defaults = {
            'name': f'Test Lesson {cls.random_string(5)}',
            'class_id': class_obj.id,
            'course_id': class_obj.course_id.id,
            'start_time': today + timedelta(days=1),
            'end_time': today + timedelta(days=1, hours=2),
            'duration': 2.0,
            'lesson_type': 'theory',
            'location_type': 'onsite',
        }
        defaults.update(kwargs)
        return env['eb.lesson.lesson'].create(defaults)


class EnrollmentFactory(BaseFactory):
    """Factory for creating eb.course.enrollment records."""
    
    @classmethod
    def create(cls, env, course=None, student=None, **kwargs):
        """Create an enrollment with default values."""
        if not course:
            course = CourseFactory.create(env)
        if not student:
            student = StudentFactory.create(env)
            
        defaults = {
            'course_id': course.id,
            'student_id': student.id,
            'enrollment_date': datetime.now(),
            'state': 'unpaid',
            'amount': course.price,
        }
        defaults.update(kwargs)
        return env['eb.course.enrollment'].create(defaults)


class AttendanceFactory(BaseFactory):
    """Factory for creating eb.attendance.attendance records."""
    
    @classmethod
    def create(cls, env, lesson=None, student=None, **kwargs):
        """Create an attendance record with default values."""
        if not lesson:
            lesson = LessonFactory.create(env)
        if not student:
            student = StudentFactory.create(env)
            
        defaults = {
            'lesson_id': lesson.id,
            'student_id': student.id,
            'attendance_date': lesson.start_time.date(),
            'status': 'present',
            'check_in_time': lesson.start_time,
        }
        defaults.update(kwargs)
        return env['eb.attendance.attendance'].create(defaults)


class QuizFactory(BaseFactory):
    """Factory for creating eb.quiz records."""
    
    @classmethod
    def create(cls, env, course=None, **kwargs):
        """Create a quiz with default values."""
        if not course:
            course = CourseFactory.create(env)
            
        defaults = {
            'name': f'Test Quiz {cls.random_string(5)}',
            'course_id': course.id,
            'description': 'Test quiz description',
            'time_limit': 60,
            'passing_score': 70.0,
            'is_active': True,
        }
        defaults.update(kwargs)
        return env['eb.quiz'].create(defaults)


class CertificateFactory(BaseFactory):
    """Factory for creating eb.certificate.certificate records."""
    
    @classmethod
    def create(cls, env, student=None, course=None, **kwargs):
        """Create a certificate with default values."""
        if not student:
            student = StudentFactory.create(env)
        if not course:
            course = CourseFactory.create(env)
            
        defaults = {
            'student_id': student.id,
            'course_id': course.id,
            'issue_date': datetime.now().date(),
            'status': 'issued',
            'certificate_number': f'CERT-{cls.random_string(8).upper()}',
        }
        defaults.update(kwargs)
        return env['eb.certificate.certificate'].create(defaults)


class LocationFactory(BaseFactory):
    """Factory for creating eb.location.location records."""
    
    @classmethod
    def create(cls, env, **kwargs):
        """Create a location with default values."""
        defaults = {
            'name': f'Test Location {cls.random_string(5)}',
            'address': f'{random.randint(1, 999)} Test Street',
            'city': 'Ho Chi Minh City',
            'state': 'Ho Chi Minh',
            'country_id': env.ref('base.vn').id,
            'is_active': True,
        }
        defaults.update(kwargs)
        return env['eb.location.location'].create(defaults)


class RoomFactory(BaseFactory):
    """Factory for creating eb.room.room records."""
    
    @classmethod
    def create(cls, env, location=None, **kwargs):
        """Create a room with default values."""
        if not location:
            location = LocationFactory.create(env)
            
        defaults = {
            'name': f'Room {cls.random_string(3).upper()}',
            'location_id': location.id,
            'capacity': random.randint(10, 50),
            'room_type': 'classroom',
            'is_active': True,
        }
        defaults.update(kwargs)
        return env['eb.room.room'].create(defaults)
