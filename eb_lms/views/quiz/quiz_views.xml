<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_eb_quiz_form" model="ir.ui.view">
        <field name="name">eb.quiz.form</field>
        <field name="model">eb.quiz</field>
        <field name="arch" type="xml">
            <form string="Đề kiểm tra">
                <header>
                    <button name="action_publish" string="Đánh dấu sẵn sàng" type="object" class="oe_highlight"
                            invisible="state != 'draft'" confirm="Bạn có chắc chắn muốn đánh dấu quiz này là sẵn sàng không?"/>
                    <button name="action_close" string="Lưu trữ" type="object"
                            invisible="state == 'archived'" confirm="Bạn có chắc chắn muốn lưu trữ quiz này không?"/>
                    <button name="action_reset_to_draft" string="Đặt lại thành nháp" type="object"
                            invisible="state == 'draft'" confirm="Bạn có chắc chắn muốn đặt lại đề kiểm tra này thành nháp không?"/>
                    <button name="action_calculate_statistics" string="Tính toán thống kê" type="object"
                            invisible="state == 'draft'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,ready,archived"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_questions" type="object" class="oe_stat_button" icon="fa-question-circle">
                            <field name="question_count" widget="statinfo" string="Câu hỏi"/>
                        </button>
                        <button name="action_view_attempts" type="object" class="oe_stat_button" icon="fa-pencil-square-o">
                            <field name="attempt_count" widget="statinfo" string="Lần làm bài"/>
                        </button>
                        <button name="action_view_results" type="object" class="oe_stat_button" icon="fa-trophy">
                            <field name="student_count" widget="statinfo" string="Học viên"/>
                        </button>
                        <button name="action_view_analytics" type="object" class="oe_stat_button" icon="fa-bar-chart">
                            <span>Phân tích</span>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Nhập tên đề kiểm tra (ví dụ: Kiểm tra giữa kỳ môn Toán)"/>
                        </h1>
                        <h3>
                            <field name="code" readonly="1"/>
                        </h3>
                    </div>

                    <!-- Thông tin cơ bản -->
                    <group string="Thông tin cơ bản">
                        <group>
                            <field name="quiz_type" placeholder="Chọn loại kiểm tra"/>
                            <field name="subject_id" placeholder="Chọn môn học liên quan"/>
                            <field name="course_id" placeholder="Chọn khóa học (nếu có)"/>
                            <field name="class_id" placeholder="Chọn lớp học (nếu có)"/>
                        </group>
                        <group>
                            <field name="instructor_id" placeholder="Chọn giảng viên phụ trách"/>
                            <field name="is_currently_published" readonly="1"/>
                            <field name="published_lesson_count" readonly="1" invisible="not published_lesson_count"/>
                            <!-- Deprecated fields - hidden -->
                            <field name="is_published" invisible="1"/>
                            <field name="publish_date" invisible="1"/>
                        </group>
                    </group>

                    <!-- Cài đặt điểm số và thời gian -->
                    <group string="Cài đặt điểm số và thời gian">
                        <group>
                            <field name="max_score" placeholder="Ví dụ: 100.0"/>
                            <field name="passing_score" placeholder="Ví dụ: 50.0"/>
                        </group>
                        <group>
                            <field name="time_limit" placeholder="Thời gian tính bằng phút (ví dụ: 60)"/>
                        </group>
                    </group>

                    <!-- Thời gian mở đề -->
                    <group string="Thời gian mở đề">
                        <group>
                            <field name="start_date" placeholder="Chọn ngày giờ bắt đầu cho phép làm bài"/>
                            <field name="end_date" placeholder="Chọn ngày giờ kết thúc cho phép làm bài"/>
                        </group>
                        <group>
                            <!-- Trống để cân bằng layout -->
                        </group>
                    </group>

                    <!-- Cài đặt hiển thị -->
                    <group string="Cài đặt hiển thị">
                        <group>
                            <field name="is_randomized"/>
                            <field name="show_correct_answers"/>
                        </group>
                        <group>
                            <field name="show_result_immediately"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả" name="description">
                            <field name="description"
                                   placeholder="Nhập mô tả chi tiết về đề kiểm tra này...&#10;&#10;Ví dụ:&#10;- Mục tiêu của đề kiểm tra&#10;- Nội dung kiến thức được đánh giá&#10;- Yêu cầu đặc biệt (nếu có)"/>
                        </page>
                        <page string="Hướng dẫn" name="instruction">
                            <field name="instruction"
                                   placeholder="Nhập hướng dẫn chi tiết cho học viên về cách làm bài...&#10;&#10;Ví dụ:&#10;- Đọc kỹ đề bài trước khi trả lời&#10;- Chọn đáp án đúng nhất&#10;- Kiểm tra lại bài làm trước khi nộp&#10;- Lưu ý về thời gian làm bài"/>
                        </page>
                        <page string="Câu hỏi" name="questions">
                            <field name="question_ids" nolabel="1">
                                <list string="Danh sách câu hỏi" editable="bottom" create="true" delete="true">
                                    <field name="answer_ids" column_invisible="1"/>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name" string="Nội dung câu hỏi"/>
                                    <field name="question_type" string="Loại"/>
                                    <field name="score" string="Điểm" sum="Tổng điểm"/>
                                    <field name="difficulty" string="Độ khó"/>
                                    <field name="answer_count" string="Số đáp án"/>
                                    <button name="action_view_question_detail" type="object"
                                            string="Chi tiết" class="btn-link"
                                            icon="fa-eye" title="Xem chi tiết câu hỏi"/>
                                    <button name="action_view_answers" type="object"
                                            string="Đáp án" class="btn-link"
                                            icon="fa-list-ul" title="Xem và quản lý đáp án"/>
                                </list>
                                <form string="Câu hỏi">
                                    <group>
                                        <group>
                                            <field name="question_type" placeholder="Chọn loại câu hỏi"/>
                                            <field name="difficulty" placeholder="Chọn độ khó"/>
                                            <field name="score" placeholder="Điểm số"/>
                                        </group>
                                        <group>
                                            <field name="sequence" placeholder="Thứ tự"/>
                                            <field name="is_required"/>
                                            <field name="active"/>
                                        </group>
                                    </group>
                                    <field name="name" string="Nội dung câu hỏi"
                                           placeholder="Nhập nội dung câu hỏi..."/>
                                    <field name="explanation" string="Giải thích đáp án"
                                           placeholder="Giải thích đáp án (tùy chọn)"/>
                                </form>
                            </field>
                        </page>
                        <page string="Thống kê" name="statistics" invisible="state == 'draft'">
                            <group>
                                <group>
                                    <field name="average_score"/>
                                    <field name="pass_rate" widget="percentage"/>
                                </group>
                                <group>
                                    <!-- Placeholder for future statistics -->
                                </group>
                            </group>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <button name="action_calculate_statistics" string="Tính toán lại thống kê" type="object" class="btn btn-primary"/>
                                </div>
                            </div>
                            <p class="text-muted mt-2">
                                <i class="fa fa-info-circle"/> Thống kê chi tiết về từng câu hỏi có thể được xem trong mục "Thống kê câu hỏi" trong menu Đề kiểm tra.
                            </p>
                        </page>
                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_eb_quiz_list" model="ir.ui.view">
        <field name="name">eb.quiz.list</field>
        <field name="model">eb.quiz</field>
        <field name="arch" type="xml">
            <list string="Đề kiểm tra">
                <field name="name"/>
                <field name="code"/>
                <field name="quiz_type"/>
                <field name="subject_id"/>
                <field name="course_id"/>
                <field name="class_id"/>
                <field name="instructor_id"/>
                <field name="question_count"/>
                <field name="max_score"/>
                <field name="passing_score"/>
                <field name="time_limit"/>
                <field name="is_currently_published"/>
                <field name="published_lesson_count"/>
                <field name="state"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_eb_quiz_search" model="ir.ui.view">
        <field name="name">eb.quiz.search</field>
        <field name="model">eb.quiz</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm đề kiểm tra">
                <field name="name"/>
                <field name="code"/>
                <field name="subject_id"/>
                <field name="course_id"/>
                <field name="class_id"/>
                <field name="instructor_id"/>
                <field name="is_currently_published"/>
                <field name="published_lesson_count"/>
                <filter string="Đang được công bố" name="currently_published" domain="[('is_currently_published', '=', True)]"/>
                <filter string="Chưa được công bố" name="not_published" domain="[('is_currently_published', '=', False)]"/>
                <separator/>
                <filter string="Nháp" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Sẵn sàng" name="ready" domain="[('state', '=', 'ready')]"/>
                <filter string="Đã lưu trữ" name="archived" domain="[('state', '=', 'archived')]"/>
                <filter string="Đã đóng" name="closed" domain="[('state', '=', 'closed')]"/>
                <group expand="0" string="Nhóm theo">
                    <filter string="Loại kiểm tra" name="group_by_type" context="{'group_by': 'quiz_type'}"/>
                    <filter string="Môn học" name="group_by_subject" context="{'group_by': 'subject_id'}"/>
                    <filter string="Khóa học" name="group_by_course" context="{'group_by': 'course_id'}"/>
                    <filter string="Lớp học" name="group_by_class" context="{'group_by': 'class_id'}"/>
                    <filter string="Giảng viên" name="group_by_instructor" context="{'group_by': 'instructor_id'}"/>
                    <filter string="Trạng thái" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_eb_quiz_kanban" model="ir.ui.view">
        <field name="name">eb.quiz.kanban</field>
        <field name="model">eb.quiz</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="id"/>
                <field name="name"/>
                <field name="code"/>
                <field name="quiz_type"/>
                <field name="subject_id"/>
                <field name="course_id"/>
                <field name="class_id"/>
                <field name="instructor_id"/>
                <field name="question_count"/>
                <field name="state"/>
                <field name="is_published"/>
                <templates>
                    <t t-name="card">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <field name="name"/>
                                </strong>
                                <div class="o_kanban_record_subtitle">
                                    <field name="code"/> - <field name="quiz_type"/>
                                </div>
                                <div>
                                    <span t-if="record.subject_id.raw_value">
                                        <field name="subject_id"/>
                                    </span>
                                    <span t-if="record.class_id.raw_value">
                                        - <field name="class_id"/>
                                    </span>
                                </div>
                                <div>
                                    <span class="badge badge-pill" t-attf-class="badge-#{record.state.raw_value == 'draft' ? 'secondary' : record.state.raw_value == 'published' ? 'success' : 'danger'}">
                                        <field name="state"/>
                                    </span>
                                    <span class="badge badge-pill badge-info">
                                        <i class="fa fa-question-circle" title="Số lượng câu hỏi"/> <field name="question_count"/>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action -->
    <record id="action_eb_quiz" model="ir.actions.act_window">
        <field name="name">Đề kiểm tra</field>
        <field name="res_model">eb.quiz</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_eb_quiz_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo đề kiểm tra đầu tiên của bạn
            </p>
            <p>
                Đề kiểm tra là tập hợp các câu hỏi được sử dụng để đánh giá kiến thức và kỹ năng của học viên.
            </p>
        </field>
    </record>


</odoo>
