# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

_logger = logging.getLogger(__name__)

def migrate(cr, version):
    """
    Xóa column max_attempts khỏi bảng eb_quiz.
    
    Loại bỏ chức năng giới hạn số lần thử quiz để đơn giản hóa hệ thống.
    """
    if not version:
        return

    _logger.info("Bắt đầu migration: Xóa column max_attempts khỏi bảng eb_quiz")
    
    # Kiểm tra xem column có tồn tại không
    cr.execute("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'eb_quiz'
        AND column_name = 'max_attempts'
    """)
    
    if cr.fetchone():
        _logger.info("Tìm thấy column max_attempts, tiến hành xóa...")
        
        # Xóa constraint check_max_attempts_positive nếu tồn tại
        cr.execute("""
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'eb_quiz'
            AND constraint_name = 'eb_quiz_check_max_attempts_positive'
        """)
        
        if cr.fetchone():
            _logger.info("Xóa constraint check_max_attempts_positive")
            cr.execute("ALTER TABLE eb_quiz DROP CONSTRAINT eb_quiz_check_max_attempts_positive")
        
        # Xóa column max_attempts
        _logger.info("Xóa column max_attempts")
        cr.execute("ALTER TABLE eb_quiz DROP COLUMN max_attempts")
        
        _logger.info("Đã xóa thành công column max_attempts khỏi bảng eb_quiz")
    else:
        _logger.info("Column max_attempts không tồn tại, bỏ qua migration")
    
    _logger.info("Hoàn thành migration: Xóa column max_attempts")
